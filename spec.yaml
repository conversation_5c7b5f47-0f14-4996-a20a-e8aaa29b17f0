openapi: 3.0.0
info:
  title: Personalization API
  version: 1.0.0
  description: API for personalization

servers:
  - url: http://localhost:8081
paths:
  /page:
    get:
      tags:
        - pages
      summary: Get a page's widget tree
      operationId: getPage
      parameters:
        - in: query
          name: path
          required: true
          schema:
            type: string
            format: path
        - in: query
          name: params
          schema:
            type: string
      responses:
        "200":
          description: Page response
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PageResponse"
  /feature-flags:
    get:
      tags:
        - feature-flags
      summary: Get eligible feature flags for a user by on his allocated group
      operationId: getFlags
      responses:
        "200":
          description: User eligible feature flags response
          content:
            application/json:
              schema:
                type: object
                additionalProperties:
                  type: boolean
  /stories:
    get:
      tags:
        - stories
      summary: Get eligible stories by page
      operationId: getStories
      parameters:
        - in: query
          name: page
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Get eligible stories by page
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StoryResponse"
  /story-reaction:
    post:
      tags:
        - stories
      operationId: setStoryReaction
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/StoryReactionRequest"
      responses:
        "204":
          description: User Reaction recorded
  /widgets/{widgetId}/interaction:
    get:
      tags:
        - Widget Interaction
      summary: Get the latest view information for a widget interaction
      operationId: getWidgetInteractionSummary
      parameters:
        - name: widgetId
          in: path
          required: true
          schema:
            type: string
          description: Unique identifier of the widget
      responses:
        "200":
          description: Latest widget view information retrieved successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/WidgetInteractionResponse"
        "404":
          description: Widget view information not found
    post:
      tags:
        - Widget interaction
      summary: Record a new interaction of a widget
      operationId: recordWidgetInteraction
      parameters:
        - name: widgetId
          in: path
          required: true
          schema:
            type: string
          description: Unique identifier of the widget
      responses:
        "200":
          description: Widget interaction recorded successfully
        "400":
          description: Invalid request
        "404":
          description: Widget not found
  /polls/{pollId}:
    get:
      tags:
        - polls
      summary: Get poll by ID
      operationId: getPoll
      parameters:
        - name: pollId
          in: path
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Poll details
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PollResponse"
        "404":
          description: Poll not found
  /polls/{pollId}/questions/{questionId}/answer:
    post:
      tags:
        - polls
      summary: Submit an answer to a poll question
      operationId: submitPollQuestion
      parameters:
        - name: pollId
          in: path
          required: true
          schema:
            type: string
        - name: questionId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PollQuestionSubmitRequest"
      responses:
        "200":
          description: Answer recorded successfully
        "400":
          description: Invalid request
        "404":
          description: Poll or question not found
  /polls/{pollId}/questions/{questionId}/comment:
    post:
      tags:
        - polls
      summary: Add comment to a submitted an answer
      operationId: addPollComment
      parameters:
        - name: pollId
          in: path
          required: true
          schema:
            type: string
        - name: questionId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PollCommentSubmitRequest"
      responses:
        "200":
          description: Comment recorded successfully
        "400":
          description: Invalid request
        "404":
          description: Poll or question not found
  /polls/{pollId}/dismiss:
    post:
      tags:
        - polls
      summary: Dismiss a poll
      operationId: dismissPoll
      parameters:
        - name: pollId
          in: path
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Poll dismissed successfully
        "404":
          description: Poll not found

  /user-context:
    get:
      tags:
        - user-context
      summary: Get unleash context for a user
      operationId: getUserContext
      responses:
        "200":
          description: Get unleash context for a user
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UserContextResponse"
  /frame:
    get:
      tags:
        - frame
      summary: Get eligible frame (widget) for user
      operationId: getFrame
      parameters:
        - in: query
          name: name
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Get eligible frame (widget) for user
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/FrameResponse"
  /nudges:
    get:
      tags:
        - nudges
      summary: Get nudges for a user
      operationId: getNudge
      parameters:
        - in: query
          name: pageUri
          required: true
          schema:
            type: string
            format: path
      responses:
        "200":
          description: Get nudges for a user
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/NudgeResponse"
  /nudges/all:
    get:
      tags:
        - nudges
      summary: Get all nudges for a user
      operationId: getAllNudges
      parameters:
        - in: query
          name: pageUri
          required: true
          schema:
            type: string
            format: path
      responses:
        "200":
          description: Get all nudges for a user
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Nudges"
  /nudges/action:
    post:
      tags:
        - nudges
      summary: Post action on a nudge
      operationId: updateNudgeAction
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/NudgeAction"
      responses:
        "200":
          description: Action recorded
  /app-config:
    get:
      tags:
        - get app config
      summary: Get app config
      operationId: getAppConfig
      responses:
        "200":
          description: Get app config
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppConfigResponse"
  /rating-nudge:
    get:
      tags:
        - get rating nudge
      summary: Get rating nudge for a user
      operationId: getRatingNudge
      parameters:
        - in: query
          name: type
          required: false
          schema:
            type: string
            format: path
      responses:
        "200":
          description: Get rating nudge for a user
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RatingNudgeResponse"
    delete:
      tags:
        - dismiss rating nudge
      summary: Dismiss rating nudge
      operationId: dismissRatingNudge
      responses:
        "200":
          description: Rating nudge dismissed
    post:
      tags:
        - ratings
      summary: Post app rating
      operationId: postRating
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PostRatingRequest"
      responses:
        "200":
          description: Post app rating
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PostRatingResponse"
components:
  schemas:
    ParamsMap:
      type: object
      additionalProperties:
        type: string
    ErrorResponse:
      type: object
      required:
        - statusCode
        - message
      properties:
        statusCode:
          type: integer
        message:
          type: string
    PageRequest:
      type: object
      required:
        - path
        - name
      properties:
        path:
          type: string
        name:
          type: string
    PageResponse:
      type: object
      required:
        - path
        - name
        - root
      properties:
        noCache:
          type: boolean
        path:
          type: string
        name:
          type: string
        id:
          type: string
        root:
          $ref: "#/components/schemas/Widget"
    FrameResponse:
      type: object
      required:
        - name
        - root
      properties:
        id:
          type: string
        name:
          type: string
        root:
          $ref: "#/components/schemas/Widget"
    StoryResponse:
      type: object
      required:
        - page
        - root
      properties:
        page:
          type: string
        children:
          type: array
          items:
            $ref: "#/components/schemas/StoriesV2Item"
    UserContextResponse:
      type: object
      properties:
        appName:
          type: string
        environment:
          type: string
        userId:
          type: string
        sessionId:
          type: string
        remoteAddress:
          type: string
        currentTime:
          type: integer
          format: int64
        props:
          type: object
        featureFlags:
          type: object
    Nudges:
      type: object
      required:
        - nudges
      properties:
        nudges:
          type: array
          items:
            $ref: "#/components/schemas/Nudge"
    Nudge:
      type: object
      required:
        - nudgeId
        - nudge
        - category
        - type
      properties:
        nudgeId:
          type: string
        nudge:
          type: string
        name:
          type: string
        category:
          $ref: "#/components/schemas/NudgeCategory"
        type:
          $ref: "#/components/schemas/NudgeType"
        attributes:
          type: array
          items:
            $ref: "#/components/schemas/NudgeAttribute"
    NudgeResponse:
      type: object
      required:
        - nudgeId
        - nudge
      properties:
        nudgeId:
          type: string
        nudge:
          type: string
        name:
          type: string
    NudgeType:
      type: string
      enum: [BOTTOM_SHEET, FULL_PAGE, CARD, STRIP]
    NudgeCategory:
      type: string
      enum: [GENERAL, FD_JOURNEY, BOND_JOURNEY, CC_JOURNEY]
    NudgeAttribute:
      type: object
      properties:
        key:
          type: string
        value:
          type: string
    NudgeAction:
      type: object
      required:
        - nudgeId
        - actionType
      properties:
        nudgeId:
          type: string
        actionType:
          $ref: "#/components/schemas/NudgeActionType"
        category:
          type: string
    NudgeActionType:
      type: string
      enum: [ACCEPT, DISMISS]
    AppConfigResponse:
      type: object
      properties:
        config:
          type: array
          items:
            $ref: "#/components/schemas/AppConfig"
    AppConfig:
      type: object
      required:
        - name
        - value
      properties:
        name:
          type: string
        value:
          type: string
    RatingNudgeResponse:
      type: object
      properties:
        showRatingNudge:
          type: boolean
        source:
          type: string
        tag:
          type: string
    PostRatingRequest:
      type: object
      required:
        - rating
      properties:
        rating:
          type: integer
        review:
          type: string
        playStoreRatingShown:
          type: boolean
    PostRatingResponse:
      type: object
      properties:
        rating:
          type: integer
        tag:
          type: string
    StoryReactionRequest:
      type: object
      required:
        - storyId
        - hasLiked
      properties:
        storyId:
          type: string
        hasLiked:
          type: boolean
    PathType:
      type: string
      enum: [inapp, upswing, external, bottomSheet, hotwire]
    ImageType:
      type: string
      enum: [raster, svg]
    CollectionWidgetType:
      type: string
      enum: [fdType, bankType]
    StaggeredGridType:
      type: string
      enum: [aligned, staggered]
    TitleType:
      type: string
      enum: [mainHeader, subHeader, centeredWithDivider]
    AnimationType:
      type: string
      enum: [lottie, dotLottie, gif, video]
    MediaFitType:
      type: string
      enum: [fill, contain, cover, fitWidth, fitHeight, none, scaleDown]
    SpacingType:
      type: string
      enum: [small, medium, large, extraLarge, xxl]
    StoryType:
      enum: [image, video]
      type: string
    StoryCTAType:
      enum: [image, animation]
      type: string
    DataKeyVariableType:
      enum:
        [
          string,
          currency,
          date,
          date_time,
          user_first_name,
          2f_percent,
          percent,
          short_currency,
          number,
          currency_2f,
          countdown,
        ]
      type: string
    TextStyle:
      enum: [heading1, heading2, heading3, heading4, body1, body2, caption]
      type: string
    PageIndicatorType:
      enum: [dotWrapped, dot]
      type: string
    BondsCollectionCardType:
      type: string
      enum: [featured, standard, compact, card, indexedCard]
    BondsMediaType:
      type: string
      enum: [image, video]
    RedirectPresentation:
      type: string
      enum: [push, pop, replace, reset, popAndPush]
    CardContentSpacingType:
      type: string
      enum: [noSpacing, smallSpacing, mediumSpacing, largeSpacing]
    CardContentRadiusType:
      type: string
      enum: [noRadius, smallRadius, mediumRadius, largeRadius]
    VideoControlsPosition:
      type: string
      enum: [topRight, bottomRight, topLeft, bottomLeft, center]
    BankInvestabilityStatus:
      type: string
      enum: [active, inactive, rollout, comingSoon]
    RedirectLink:
      type: object
      required:
        - path
        - pathType
      properties:
        path:
          type: string
        presentation:
          $ref: "#/components/schemas/RedirectPresentation"
        pathType:
          $ref: "#/components/schemas/PathType"
        eventName:
          type: string
        analyticsEventProps:
          type: object
    DataKey:
      type: object
      required:
        - key
      properties:
        key:
          type: string
        contextVariables:
          type: array
          items:
            $ref: "#/components/schemas/DataKeyVariable"
    DataKeyVariable:
      type: object
      required:
        - name
        - type
        - value
      properties:
        name:
          type: string
        value:
          type: string
        type:
          $ref: "#/components/schemas/DataKeyVariableType"
    TableDataResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: "#/components/schemas/TableDataItem"
    TableDataItem:
      type: object
      properties:
        labelDataKey:
          $ref: "#/components/schemas/DataKey"
        valueDataKey:
          $ref: "#/components/schemas/DataKey"
        subValueDataKey:
          $ref: "#/components/schemas/DataKey"
    TabItem:
      type: object
      discriminator:
        propertyName: tabItem
        mapping:
          RegularTabItem: "#/components/schemas/RegularTabItem"
          TaggedTabItem: "#/components/schemas/TaggedTabItem"
          DefaultTabItem: "#/components/schemas/DefaultTabItem"
      oneOf:
        - $ref: "#/components/schemas/RegularTabItem"
        - $ref: "#/components/schemas/TaggedTabItem"
        - $ref: "#/components/schemas/DefaultTabItem"

    Widget:
      type: object
      discriminator:
        propertyName: widget
        mapping:
          Home: "#/components/schemas/Home"
          BasePage: "#/components/schemas/BasePage"
          BaseBottomSheet: "#/components/schemas/BaseBottomSheet"
          AppBar: "#/components/schemas/AppBar"
          Image: "#/components/schemas/Image"
          Animation: "#/components/schemas/Animation"
          Carousel: "#/components/schemas/Carousel"
          ClippedTransitionCarousel: "#/components/schemas/ClippedTransitionCarousel"
          ClippedTransitionCarouselItem: "#/components/schemas/ClippedTransitionCarouselItem"
          HorizontalListCollection: "#/components/schemas/HorizontalListCollection"
          GridCollection: "#/components/schemas/GridCollection"
          BondsGridCollection: "#/components/schemas/BondsGridCollection"
          HorizontalListSection: "#/components/schemas/HorizontalListSection"
          SingleCategoryFAQ: "#/components/schemas/SingleCategoryFAQ"
          FindMyFD: "#/components/schemas/FindMyFD"
          InvestmentSummary: "#/components/schemas/InvestmentSummary"
          BondInvestmentsSummary: "#/components/schemas/BondInvestmentsSummary"
          BondsGrid: "#/components/schemas/BondsGrid"
          AppBarProfileAction: "#/components/schemas/AppBarProfileAction"
          MarketingListWithCover: "#/components/schemas/MarketingListWithCover"
          NextBestFD: "#/components/schemas/NextBestFD"
          OneClickFD: "#/components/schemas/OneClickFD"
          MaturingFdsCarousel: "#/components/schemas/MaturingFdsCarousel"
          Stories: "#/components/schemas/Stories"
          StoriesV2: "#/components/schemas/StoriesV2"
          BottomSheet: "#/components/schemas/BottomSheet"
          StaggeredGrid: "#/components/schemas/StaggeredGrid"
          FdsInsuranceProgressTracker: "#/components/schemas/FdsInsuranceProgressTracker"
          InlineFaqs: "#/components/schemas/InlineFaqs"
          Faqs: "#/components/schemas/Faqs"
          BondDetails: "#/components/schemas/BondDetails"
          NavigationBar: "#/components/schemas/NavigationBar"
          NavigationBarItem: "#/components/schemas/NavigationBarItem"
          DataTable: "#/components/schemas/DataTable"
          BondsDataTable: "#/components/schemas/BondsDataTable"
          BondsReturnsCalculator: "#/components/schemas/BondsReturnsCalculator"
          BondsInvestButton: "#/components/schemas/BondsInvestButton"
          BondsMarketingHighlights: "#/components/schemas/BondsMarketingHighlights"
          BondsKeyHighlights: "#/components/schemas/BondsKeyHighlights"
          BondsDisclosureButton: "#/components/schemas/BondsDisclosureButton"
          BondsDisclosureDetails: "#/components/schemas/BondsDisclosureDetails"
          BondsDisclosureIssuerDetails: "#/components/schemas/BondsDisclosureIssuerDetails"
          BondsSupportedDocuments: "#/components/schemas/BondsSupportedDocuments"
          BondsSellerEntityDisclosure: "#/components/schemas/BondsSellerEntityDisclosure"
          BondsFaqs: "#/components/schemas/BondsFaqs"
          Card: "#/components/schemas/Card"
          BondsCollectionTable: "#/components/schemas/BondsCollectionTable"
          BondsCollection: "#/components/schemas/BondsCollection"
          BondsHeroCollection: "#/components/schemas/BondsHeroCollection"
          BondsExpandableCollection: "#/components/schemas/BondsExpandableCollection"
          InvestmentCards: "#/components/schemas/InvestmentCards"
          OrgInvestmentSummary: "#/components/schemas/OrgInvestmentSummary"
          OrgInvestmentCards: "#/components/schemas/OrgInvestmentCards"
          Column: "#/components/schemas/Column"
          BondsTnCAndKYCFooter: "#/components/schemas/BondsTnCAndKYCFooter"
          BondsHeroHeader: "#/components/schemas/BondsHeroHeader"
          BondsSingleMedia: "#/components/schemas/BondsSingleMedia"
          CarouselItem: "#/components/schemas/CarouselItem"
          KebabMenu: "#/components/schemas/KebabMenu"
          KeyValueTile: "#/components/schemas/KeyValueTile"
          InAppRatingInvisibleWidget: "#/components/schemas/InAppRatingInvisibleWidget"
          SuryodayCreditCardFooter: "#/components/schemas/SuryodayCreditCardFooter"
          InvestmentBasicDetails: "#/components/schemas/InvestmentBasicDetails"
          InsuranceAmountProgress: "#/components/schemas/InsuranceAmountProgress"
          KebabMenuV2: "#/components/schemas/KebabMenuV2"
          KebabMenuV3: "#/components/schemas/KebabMenuV3"
          FDReceiptKebabMenuItem: "#/components/schemas/FDReceiptKebabMenuItem"
          MenuItemV2: "#/components/schemas/MenuItemV2"
          RedirectWidget: "#/components/schemas/RedirectWidget"
          BulletPoints: "#/components/schemas/BulletPoints"
          FDCollectionTable: "#/components/schemas/FDCollectionTable"
          BankDetails: "#/components/schemas/BankDetails"
          BanksHeroHeader: "#/components/schemas/BanksHeroHeader"
          BankTableCard: "#/components/schemas/BankTableCard"
          BankTableCardItem: "#/components/schemas/BankTableCardItem"
          BankTenureCard: "#/components/schemas/BankTenureCard"
          BankFdNibCompareCard: "#/components/schemas/BankFdNibCompareCard"
          BankMessageWidget: "#/components/schemas/BankMessageWidget"
          BankNibInvestableOptions: "#/components/schemas/BankNibInvestableOptions"
          BankShareWidget: "#/components/schemas/BankShareWidget"
          BankInvestButton: "#/components/schemas/BankInvestButton"
          BanksSingleMedia: "#/components/schemas/BanksSingleMedia"
          BankWithdrawalCalculatorCard: "#/components/schemas/BankWithdrawalCalculatorCard"
          BankHighlightPointersCard: "#/components/schemas/BankHighlightPointersCard"
          BankIbCompareCard: "#/components/schemas/BankIbCompareCard"
          SuryodayCreditCardWaitListFooter: "#/components/schemas/SuryodayCreditCardWaitListFooter"
          DynamicBankFaqs: "#/components/schemas/DynamicBankFaqs"
          Text: "#/components/schemas/Text"
          Counter: "#/components/schemas/Counter"
          PollCard: "#/components/schemas/PollCard"
          HorizontalCollectionItem: "#/components/schemas/HorizontalCollectionItem"
          HorizontalCollectionSection: "#/components/schemas/HorizontalCollectionSection"
          ProfileHeader: "#/components/schemas/ProfileHeader"
          ProfileMenuItem: "#/components/schemas/ProfileMenuItem"
          ProfileFooter: "#/components/schemas/ProfileFooter"
          TabularPage: "#/components/schemas/TabularPage"
          TranslucentTag: "#/components/schemas/TranslucentTag"
          SolidTag: "#/components/schemas/SolidTag"
          DefaultWidget: "#/components/schemas/DefaultWidget"
          BankDICGCCard: "#/components/schemas/BankDICGCCard"
          TrustMarkersFooter: "#/components/schemas/TrustMarkersFooter"
          SuryodayCreditCardAddressChangeFooter: "#/components/schemas/SuryodayCreditCardAddressChangeFooter"
          FDSearchAndFilter: "#/components/schemas/FDSearchAndFilter"
          BondsSearchAndFilter: "#/components/schemas/BondsSearchAndFilter"
          FDRewardOptInWidget: "#/components/schemas/FDRewardOptInWidget"
          UnitySavingAccountCreateFooter: "#/components/schemas/UnitySavingAccountCreateFooter"
          UnitySavingAccountBankEntryPoint: "#/components/schemas/UnitySavingAccountBankEntryPoint"
          UnitySavingAccountInterestRateTable: "#/components/schemas/UnitySavingAccountInterestRateTable"
      oneOf:
        - $ref: "#/components/schemas/Home"
        - $ref: "#/components/schemas/BasePage"
        - $ref: "#/components/schemas/AppBar"
        - $ref: "#/components/schemas/Image"
        - $ref: "#/components/schemas/Animation"
        - $ref: "#/components/schemas/Carousel"
        - $ref: "#/components/schemas/HorizontalListCollection"
        - $ref: "#/components/schemas/GridCollection"
        - $ref: "#/components/schemas/BondsGridCollection"
        - $ref: "#/components/schemas/HorizontalListSection"
        - $ref: "#/components/schemas/SingleCategoryFAQ"
        - $ref: "#/components/schemas/FindMyFD"
        - $ref: "#/components/schemas/InvestmentSummary"
        - $ref: "#/components/schemas/BondInvestmentsSummary"
        - $ref: "#/components/schemas/BondsGrid"
        - $ref: "#/components/schemas/AppBarProfileAction"
        - $ref: "#/components/schemas/ClippedTransitionCarousel"
        - $ref: "#/components/schemas/ClippedTransitionCarouselItem"
        - $ref: "#/components/schemas/MarketingListWithCover"
        - $ref: "#/components/schemas/NextBestFD"
        - $ref: "#/components/schemas/OneClickFD"
        - $ref: "#/components/schemas/MaturingFdsCarousel"
        - $ref: "#/components/schemas/Stories"
        - $ref: "#/components/schemas/StoriesV2"
        - $ref: "#/components/schemas/BottomSheet"
        - $ref: "#/components/schemas/StaggeredGrid"
        - $ref: "#/components/schemas/FdsInsuranceProgressTracker"
        - $ref: "#/components/schemas/InlineFaqs"
        - $ref: "#/components/schemas/Faqs"
        - $ref: "#/components/schemas/NavigationBar"
        - $ref: "#/components/schemas/NavigationBarItem"
        - $ref: "#/components/schemas/BondDetails"
        - $ref: "#/components/schemas/DataTable"
        - $ref: "#/components/schemas/BondsDataTable"
        - $ref: "#/components/schemas/BondsReturnsCalculator"
        - $ref: "#/components/schemas/BondsInvestButton"
        - $ref: "#/components/schemas/BondsMarketingHighlights"
        - $ref: "#/components/schemas/BondsKeyHighlights"
        - $ref: "#/components/schemas/BondsDisclosureButton"
        - $ref: "#/components/schemas/BondsDisclosureDetails"
        - $ref: "#/components/schemas/BondsDisclosureIssuerDetails"
        - $ref: "#/components/schemas/BondsSupportedDocuments"
        - $ref: "#/components/schemas/BondsSellerEntityDisclosure"
        - $ref: "#/components/schemas/BondsFaqs"
        - $ref: "#/components/schemas/Card"
        - $ref: "#/components/schemas/BondsCollectionTable"
        - $ref: "#/components/schemas/BondsCollection"
        - $ref: "#/components/schemas/BondsExpandableCollection"
        - $ref: "#/components/schemas/BondsHeroCollection"
        - $ref: "#/components/schemas/InvestmentCards"
        - $ref: "#/components/schemas/OrgInvestmentSummary"
        - $ref: "#/components/schemas/OrgInvestmentCards"
        - $ref: "#/components/schemas/Column"
        - $ref: "#/components/schemas/BondsTnCAndKYCFooter"
        - $ref: "#/components/schemas/BondsHeroHeader"
        - $ref: "#/components/schemas/BondsSingleMedia"
        - $ref: "#/components/schemas/CarouselItem"
        - $ref: "#/components/schemas/KebabMenu"
        - $ref: "#/components/schemas/KeyValueTile"
        - $ref: "#/components/schemas/InAppRatingInvisibleWidget"
        - $ref: "#/components/schemas/SuryodayCreditCardFooter"
        - $ref: "#/components/schemas/InvestmentBasicDetails"
        - $ref: "#/components/schemas/InsuranceAmountProgress"
        - $ref: "#/components/schemas/KebabMenuV2"
        - $ref: "#/components/schemas/KebabMenuV3"
        - $ref: "#/components/schemas/FDReceiptKebabMenuItem"
        - $ref: "#/components/schemas/MenuItemV2"
        - $ref: "#/components/schemas/RedirectWidget"
        - $ref: "#/components/schemas/BulletPoints"
        - $ref: "#/components/schemas/FDCollectionTable"
        - $ref: "#/components/schemas/BankTenureCard"
        - $ref: "#/components/schemas/BankDetails"
        - $ref: "#/components/schemas/BanksHeroHeader"
        - $ref: "#/components/schemas/BankTableCard"
        - $ref: "#/components/schemas/BankTableCardItem"
        - $ref: "#/components/schemas/BankTenureCard"
        - $ref: "#/components/schemas/BankFdNibCompareCard"
        - $ref: "#/components/schemas/BankMessageWidget"
        - $ref: "#/components/schemas/BankNibInvestableOptions"
        - $ref: "#/components/schemas/BankShareWidget"
        - $ref: "#/components/schemas/BankInvestButton"
        - $ref: "#/components/schemas/BanksSingleMedia"
        - $ref: "#/components/schemas/BankWithdrawalCalculatorCard"
        - $ref: "#/components/schemas/BankHighlightPointersCard"
        - $ref: "#/components/schemas/BankIbCompareCard"
        - $ref: "#/components/schemas/SuryodayCreditCardWaitListFooter"
        - $ref: "#/components/schemas/DynamicBankFaqs"
        - $ref: "#/components/schemas/BaseBottomSheet"
        - $ref: "#/components/schemas/Text"
        - $ref: "#/components/schemas/Counter"
        - $ref: "#/components/schemas/PollCard"
        - $ref: "#/components/schemas/HorizontalCollectionItem"
        - $ref: "#/components/schemas/HorizontalCollectionSection"
        - $ref: "#/components/schemas/ProfileHeader"
        - $ref: "#/components/schemas/ProfileMenuItem"
        - $ref: "#/components/schemas/ProfileFooter"
        - $ref: "#/components/schemas/TabularPage"
        - $ref: "#/components/schemas/TranslucentTag"
        - $ref: "#/components/schemas/SolidTag"
        - $ref: "#/components/schemas/DefaultWidget"
        - $ref: "#/components/schemas/BankDICGCCard"
        - $ref: "#/components/schemas/TrustMarkersFooter"
        - $ref: "#/components/schemas/SuryodayCreditCardAddressChangeFooter"
        - $ref: "#/components/schemas/FDSearchAndFilter"
        - $ref: "#/components/schemas/BondsSearchAndFilter"
        - $ref: "#/components/schemas/FDRewardOptInWidget"
        - $ref: "#/components/schemas/UnitySavingAccountCreateFooter"
        - $ref: "#/components/schemas/UnitySavingAccountBankEntryPoint"
        - $ref: "#/components/schemas/UnitySavingAccountInterestRateTable"
    Home:
      type: object
      required:
        - widget
        - id
        - props
        - slots
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/HomeProps"
        slots:
          $ref: "#/components/schemas/HomeSlots"
    HomeProps:
      type: object
      required:
        - extendBodyBehindAppBar
      properties:
        extendBodyBehindAppBar:
          type: boolean
        viewEventProps:
          type: object
    HomeSlots:
      type: object
      required:
        - body
      properties:
        header:
          $ref: "#/components/schemas/Widget"
        body:
          type: array
          items:
            $ref: "#/components/schemas/Widget"
    BaseBottomSheet:
      type: object
      required:
        - widget
        - id
        - slots
      properties:
        widget:
          type: string
        id:
          type: string
        slots:
          $ref: "#/components/schemas/BaseBottomSheetSlots"
        props:
          $ref: "#/components/schemas/BaseBottomSheetProps"
    BaseBottomSheetSlots:
      type: object
      required:
        - body
      properties:
        body:
          type: array
          items:
            $ref: "#/components/schemas/Widget"
        ctaWidget:
          $ref: "#/components/schemas/Widget"
    BaseBottomSheetProps:
      type: object
      properties:
        backgroundColor:
          type: string
    BasePage:
      type: object
      required:
        - widget
        - id
        - props
        - slots
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/BasePageProps"
        slots:
          $ref: "#/components/schemas/BasePageSlots"
    BasePageProps:
      type: object
      properties:
        extendBodyBehindAppBar:
          type: boolean
        backgroundColor:
          type: string
        isEmptyState:
          type: boolean
        viewEventProps:
          type: object
    BasePageSlots:
      type: object
      required:
        - body
      properties:
        header:
          $ref: "#/components/schemas/Widget"
        body:
          type: array
          items:
            $ref: "#/components/schemas/Widget"
        emptyState:
          $ref: "#/components/schemas/Widget"
        footer:
          $ref: "#/components/schemas/Widget"
    AppBar:
      type: object
      required:
        - widget
        - id
        - props
        - slots
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/AppBarProps"
        slots:
          $ref: "#/components/schemas/AppBarSlots"
    AppBarProps:
      type: object
      properties:
        title:
          type: string
        backgroundColor:
          type: string
        isPinned:
          type: boolean
    AppBarSlots:
      type: object
      properties:
        left:
          type: array
          items:
            $ref: "#/components/schemas/Widget"
        right:
          type: array
          items:
            $ref: "#/components/schemas/Widget"
    Image:
      type: object
      required:
        - widget
        - id
        - props
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/ImageProps"
    ImageSource:
      type: object
      required:
        - url
        - screenType
      properties:
        url:
          type: string
        screenType:
          $ref: "#/components/schemas/ScreenType"
    ScreenType:
      type: string
      enum: [mobile, tablet, desktop]
    ImageProps:
      type: object
      required:
        - url
        - type
      properties:
        srcSet:
          type: array
          items:
            $ref: "#/components/schemas/ImageSource"
        url:
          type: string
        redirectLink:
          $ref: "#/components/schemas/RedirectLink"
        width:
          type: number
          format: float
        height:
          type: number
          format: float
        widthPercentage:
          type: number
          format: float
        aspectRatio:
          type: number
          format: float
        useLocalAssets:
          type: boolean
        hideLoadingShimmer:
          type: boolean
        type:
          $ref: "#/components/schemas/ImageType"
        fit:
          $ref: "#/components/schemas/MediaFitType"
        analyticsEventProps:
          type: object
    Animation:
      type: object
      required:
        - widget
        - id
        - props
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/AnimationProps"
    AnimationProps:
      type: object
      required:
        - url
        - type
        - loop
        - playOnVisibility
      properties:
        url:
          type: string
        redirectLink:
          $ref: "#/components/schemas/RedirectLink"
        width:
          type: number
          format: float
        height:
          type: number
          format: float
        widthPercentage:
          type: number
          format: float
        aspectRatio:
          type: number
          format: float
        loop:
          type: boolean
        playOnVisibility:
          type: boolean
        videoControlsPosition:
          $ref: "#/components/schemas/VideoControlsPosition"
        type:
          $ref: "#/components/schemas/AnimationType"
        fit:
          $ref: "#/components/schemas/MediaFitType"
        useLocalAssets:
          type: boolean
    Carousel:
      type: object
      required:
        - widget
        - id
        - slots
      properties:
        widget:
          type: string
        id:
          type: string
        slots:
          $ref: "#/components/schemas/CarouselSlots"
        props:
          $ref: "#/components/schemas/CarouselProps"
    CarouselSlots:
      type: object
      required:
        - children
      properties:
        children:
          type: array
          items:
            $ref: "#/components/schemas/CarouselItem"
    CarouselProps:
      type: object
      required:
        - height
      properties:
        height:
          type: number
          format: float
        loop:
          type: boolean
        autoSwipe:
          type: boolean
        heightPercentage:
          type: number
          format: float
        pageIndicatorType:
          $ref: "#/components/schemas/PageIndicatorType"
    CarouselItem:
      type: object
      required:
        - widget
        - id
        - slots
      properties:
        widget:
          type: string
        id:
          type: string
        slots:
          $ref: "#/components/schemas/CarouselItemSlots"
        props:
          $ref: "#/components/schemas/CarouselItemProps"
    CarouselItemSlots:
      type: object
      required:
        - content
      properties:
        content:
          $ref: "#/components/schemas/Widget"
    CarouselItemProps:
      type: object
      properties:
        autoPlayDurationInMillis:
          type: integer
    ClippedTransitionCarousel:
      type: object
      required:
        - widget
        - id
        - slots
      properties:
        widget:
          type: string
        id:
          type: string
        slots:
          $ref: "#/components/schemas/ClippedTransitionCarouselSlots"
        props:
          $ref: "#/components/schemas/ClippedTransitionCarouselProps"
    ClippedTransitionCarouselSlots:
      type: object
      properties:
        children:
          type: array
          items:
            $ref: "#/components/schemas/ClippedTransitionCarouselItem"
    ClippedTransitionCarouselItem:
      type: object
      required:
        - id
        - widget
        - slots
      properties:
        id:
          type: string
        widget:
          type: string
        slots:
          $ref: "#/components/schemas/ClippedTransitionCarouselItemSlots"
        props:
          $ref: "#/components/schemas/ClippedTransitionCarouselItemProps"
    ClippedTransitionCarouselItemSlots:
      type: object
      required:
        - stacked
        - clipped
      properties:
        stacked:
          $ref: "#/components/schemas/Widget"
        clipped:
          $ref: "#/components/schemas/Widget"
    ClippedTransitionCarouselItemProps:
      type: object
      properties:
        backgroundColor:
          type: string
        stackedWidgetPercentage:
          type: number
          format: float
        clippedWidgetPercentage:
          type: number
          format: float
        redirectLink:
          $ref: "#/components/schemas/RedirectLink"
    ClippedTransitionCarouselProps:
      type: object
      required:
        - height
      properties:
        height:
          type: number
          format: float
        heightPercentage:
          type: number
          format: float
        loop:
          type: boolean
        autoPlayDurationInMillis:
          type: integer
        disableAppBarPadding:
          type: boolean
        bannerPath:
          type: string
        bannerCount:
          type: integer
    HorizontalListCollection:
      type: object
      required:
        - widget
        - id
        - props
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/HorizontalListCollectionProps"
    HorizontalListCollectionProps:
      type: object
      required:
        - collectionWidgetType
      properties:
        collectionId:
          type: string
          deprecated: true
        collectionName:
          type: string
        maxCount:
          type: integer
        viewAllMinLimit:
          type: integer
        collectionWidgetType:
          $ref: "#/components/schemas/CollectionWidgetType"

    BulletPoints:
      type: object
      required:
        - widget
        - id
        - props
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/BulletPointsWidgetProps"
    BulletPointsWidgetProps:
      type: object
      properties:
        pointsList:
          type: array
          items:
            $ref: "#/components/schemas/DataKey"
    GridCollection:
      type: object
      required:
        - widget
        - id
        - props
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/GridCollectionProps"
    GridCollectionProps:
      type: object
      required:
        - collectionWidgetType
      properties:
        collectionId:
          type: string
          deprecated: true
        collectionName:
          type: string
        backgroundColor:
          type: string
        collectionWidgetType:
          $ref: "#/components/schemas/CollectionWidgetType"
    HorizontalListSection:
      type: object
      required:
        - widget
        - id
        - props
        - slots
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/HorizontalListSectionProps"
        slots:
          $ref: "#/components/schemas/WidgetListSlots"
    HorizontalListSectionProps:
      type: object
      properties:
        height:
          type: number
          format: float
        title:
          type: string
        titleType:
          $ref: "#/components/schemas/TitleType"
        backgroundColor:
          type: string
        addItemShadow:
          type: boolean
        aspectRatio:
          format: float
          type: number
        viewPort:
          format: float
          type: number
        widthPercentage:
          format: float
          type: number
        snap:
          type: boolean

    WidgetListSlots:
      type: object
      required:
        - children
      properties:
        children:
          type: array
          items:
            $ref: "#/components/schemas/Widget"
    SingleCategoryFAQ:
      type: object
      required:
        - widget
        - id
        - props
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/SingleCategoryFAQProps"
    SingleCategoryFAQProps:
      type: object
      required:
        - faqCategory
      properties:
        faqBusinessUnit:
          type: string
        faqCategory:
          type: string
        faqNamespaceIdentifier:
          type: string
        title:
          type: string
        titleType:
          $ref: "#/components/schemas/TitleType"
        maxCollapsedCount:
          type: integer
        maxViewAllFAQLabel:
          type: string
        expandFAQByDefault:
          type: boolean
        isDarkMode:
          type: boolean
        redirectLink:
          $ref: "#/components/schemas/RedirectLink"
    InvestmentSummary:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/InvestmentSummaryProps"
    InvestmentSummaryProps:
      type: object
      properties:
        isTappable:
          type: boolean
        showWhenZero:
          type: boolean
        allowMasking:
          type: boolean
    FindMyFD:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/FindMyFDProps"
        slots:
          $ref: "#/components/schemas/FindMyFDSlots"
    FindMyFDSlots:
      type: object
      required:
        - zeroState
      properties:
        zeroState:
          $ref: "#/components/schemas/Widget"
    FindMyFDProps:
      type: object
      required:
        - faqCategory
      properties:
        nibCount:
          type: integer
        ibCount:
          type: integer
        totalBankCount:
          type: integer
        title:
          type: string
        titleType:
          $ref: "#/components/schemas/TitleType"
    AppBarProfileAction:
      type: object
      required:
        - widget
        - id
        - props
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/AppBarProfileActionProps"
    AppBarProfileActionProps:
      type: object
      required:
        - width
        - height
      properties:
        width:
          type: number
          format: float
        height:
          type: number
          format: float
    MarketingListWithCover:
      type: object
      required:
        - widget
        - id
        - props
        - slots
      properties:
        id:
          type: string
        props:
          $ref: "#/components/schemas/MarketingListWithCoverProps"
        slots:
          $ref: "#/components/schemas/WidgetListSlots"
        widget:
          type: string
    MarketingListWithCoverProps:
      properties:
        aspectRatio:
          format: float
          type: number
        height:
          format: float
          type: number
        redirectLink:
          $ref: "#/components/schemas/RedirectLink"
        sliderHeight:
          format: float
          type: number
        url:
          type: string
        width:
          format: float
          type: number
      required:
        - url
      type: object
    NextBestFD:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/NextBestFDProps"
    OneClickFD:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/OneClickFDProps"
    MaturingFdsCarousel:
      properties:
        id:
          type: string
        widget:
          type: string
        props:
          $ref: "#/components/schemas/MaturingFdsCarouselProps"
      required:
        - widget
        - id
      type: object
    NextBestFDProps:
      type: object
      properties:
        title:
          type: string
        titleType:
          $ref: "#/components/schemas/TitleType"
    OneClickFDProps:
      type: object
      properties:
        title:
          type: string
        titleType:
          $ref: "#/components/schemas/TitleType"
    MaturingFdsCarouselProps:
      type: object
      properties:
        title:
          type: string
        titleType:
          $ref: "#/components/schemas/TitleType"
    Stories:
      properties:
        id:
          type: string
        props:
          $ref: "#/components/schemas/StoriesProps"
        widget:
          type: string
      required:
        - widget
        - id
        - props
      type: object
    StoriesProps:
      properties:
        storiesItemList:
          $ref: "#/components/schemas/StoriesItemList"
        title:
          type: string
        titleType:
          $ref: "#/components/schemas/TitleType"
        unseenStoriesWidget:
          $ref: "#/components/schemas/Widget"
        seenStoriesWidget:
          $ref: "#/components/schemas/Widget"
        toolTipDurationInSeconds:
          type: integer
        toolTipContent:
          type: string
      type: object
    StoriesItemList:
      required:
        - children
      properties:
        children:
          type: array
          items:
            $ref: "#/components/schemas/StoriesItem"
      type: object
    StoriesItem:
      properties:
        id:
          type: string
        backgroundColor:
          type: string
        caption:
          type: string
        ctaWidget:
          $ref: "#/components/schemas/Widget"
        fit:
          $ref: "#/components/schemas/MediaFitType"
        label:
          type: string
        durationInSeconds:
          type: integer
        type:
          $ref: "#/components/schemas/StoryType"
        url:
          type: string
      type: object

    # FAQ Components
    InlineFaqs:
      type: object
      required:
        - id
        - props
        - widget
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/FaqProps"
    Faqs:
      type: object
      required:
        - id
        - props
        - widget
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/FaqProps"
    FaqProps:
      type: object
      required:
        - faqs
      properties:
        faqs:
          type: array
          items:
            $ref: "#/components/schemas/Faq"
        faqCount:
          type: integer
        searchFieldPrefix:
          type: string
        searchFieldPrompt:
          type: array
          items:
            type: string
        categoryList:
          type: array
          items:
            type: string
        faqNamespace:
          type: string
        faqBusinessUnit:
          type: string
        faqNamespaceIdentifier:
          type: string
        path:
          type: string
        title:
          type: string
        titleType:
          $ref: "#/components/schemas/TitleType"
    Faq:
      type: object
      required:
        - faqId
        - question
        - answer
        - htmlAnswer
        - externalTags
        - userName
        - userLocation
        - userProfileImageUrl
        - upVotes
        - downVotes
      properties:
        faqId:
          type: string
        question:
          type: string
        answer:
          type: string
        htmlAnswer:
          type: string
        externalTags:
          type: array
          items:
            type: string
        userImage:
          type: string
        userName:
          type: string
        location:
          type: string
        upVotes:
          type: integer
        downVotes:
          type: integer

    BottomSheet:
      type: object
      required:
        - widget
        - id
        - slots
      properties:
        widget:
          type: string
        id:
          type: string
        slots:
          $ref: "#/components/schemas/BottomSheetSlots"
    BottomSheetSlots:
      type: object
      required:
        - body
      properties:
        body:
          type: array
          items:
            $ref: "#/components/schemas/Widget"
    StaggeredGrid:
      type: object
      required:
        - widget
        - id
        - slots
        - props
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/StaggeredGridProps"
        slots:
          $ref: "#/components/schemas/StaggeredGridSlots"
    StaggeredGridProps:
      type: object
      properties:
        title:
          type: string
        titleType:
          $ref: "#/components/schemas/TitleType"
        type:
          $ref: "#/components/schemas/StaggeredGridType"
        crossAxisCount:
          type: number
          format: float
        hasCrossAxisSpacing:
          type: boolean
        hasMainAxisSpacing:
          type: boolean
    StaggeredGridSlots:
      type: object
      required:
        - children
      properties:
        children:
          type: array
          items:
            $ref: "#/components/schemas/StaggeredGridItem"
    StaggeredGridItem:
      type: object
      required:
        - id
        - widget
        - props
        - slots
      properties:
        id:
          type: string
        widget:
          type: string
        slots:
          $ref: "#/components/schemas/StaggeredGridItemSlots"
        props:
          $ref: "#/components/schemas/StaggeredGridItemProps"
    StaggeredGridItemSlots:
      type: object
      required:
        - child
      properties:
        child:
          $ref: "#/components/schemas/Widget"
    StaggeredGridItemProps:
      type: object
      properties:
        crossAxisCellCount:
          type: integer
        title:
          type: string
        titleType:
          $ref: "#/components/schemas/TitleType"
        mainAxisCellCount:
          type: integer
    FdsInsuranceProgressTracker:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/FdsInsuranceProgressTrackerProps"
    FdsInsuranceProgressTrackerProps:
      type: object
      properties:
        title:
          type: string
        titleType:
          $ref: "#/components/schemas/TitleType"
        heading:
          type: string
        subTitles:
          type: array
          items:
            type: string
        footerText:
          type: string
    StoriesV2:
      properties:
        id:
          type: string
        props:
          $ref: "#/components/schemas/StoriesV2Props"
        slots:
          $ref: "#/components/schemas/StoriesV2Slots"
        widget:
          type: string
      required:
        - widget
        - id
        - props
      type: object
    StoriesV2Slots:
      properties:
        children:
          type: array
          items:
            $ref: "#/components/schemas/StoriesV2Item"
        unseenStories:
          $ref: "#/components/schemas/Widget"
        seenStories:
          $ref: "#/components/schemas/Widget"
      type: object
    StoriesV2Props:
      properties:
        title:
          type: string
        toolTipDurationInSeconds:
          type: integer
        toolTipContent:
          type: string
        page:
          type: string
      type: object
    StoriesV2Item:
      type: object
      properties:
        id:
          type: string
        props:
          $ref: "#/components/schemas/StoriesV2ItemProps"
        slots:
          $ref: "#/components/schemas/StoriesV2ItemSlots"
        widget:
          type: string
    StoriesV2ItemProps:
      properties:
        backgroundColor:
          type: string
        caption:
          type: string
        fit:
          $ref: "#/components/schemas/MediaFitType"
        label:
          type: string
        durationInSeconds:
          type: integer
        type:
          $ref: "#/components/schemas/StoryType"
        url:
          type: string
        likeEnabled:
          type: boolean
        shareEnabled:
          type: boolean
        storyId:
          type: string
      type: object
    StoriesContentProps:
      properties:
        backgroundColor:
          type: string
        caption:
          type: string
        fit:
          $ref: "#/components/schemas/MediaFitType"
        label:
          type: string
        durationInSeconds:
          type: integer
        type:
          $ref: "#/components/schemas/StoryType"
        url:
          type: string
        cta:
          type: object
          properties:
            type:
              $ref: "#/components/schemas/StoryCTAType"
            loopAnimation:
              type: boolean
            url:
              type: string
            redirectLink:
              $ref: "#/components/schemas/RedirectLink"
      type: object
    StoriesV2ItemSlots:
      properties:
        ctaWidget:
          $ref: "#/components/schemas/Widget"
      type: object
    BondInvestmentsSummary:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/BondInvestmentsSummaryProps"
    BondInvestmentsSummaryProps:
      type: object
      properties:
        isTappable:
          type: boolean
        showWhenZero:
          type: boolean
    BondsGrid:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/BondsGridProps"
    BondsGridProps:
      type: object
      properties:
        title:
          type: string
        titleType:
          $ref: "#/components/schemas/TitleType"
    KeyValueTile:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/KeyValueTileProps"
    KeyValueTileProps:
      type: object
      properties:
        keyText:
          $ref: "#/components/schemas/DataKey"
        valueText:
          $ref: "#/components/schemas/DataKey"
        copyContent:
          type: string
        hintText:
          type: string
        redirectLink:
          $ref: "#/components/schemas/RedirectLink"
        analyticsEventProps:
          type: object
    Column:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/ColumnProps"
        slots:
          $ref: "#/components/schemas/ColumnSlots"
    ColumnSlots:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: "#/components/schemas/Widget"
    ColumnProps:
      type: object
      properties:
        spacingType:
          $ref: "#/components/schemas/SpacingType"
        toShowDivider:
          type: boolean
    NavigationBar:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        slots:
          $ref: "#/components/schemas/NavigationBarSlots"
    NavigationBarSlots:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: "#/components/schemas/NavigationBarItem"
    NavigationBarItem:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/NavigationBarItemProps"
    NavigationBarItemProps:
      type: object
      properties:
        selectedIcon:
          $ref: "#/components/schemas/Widget"
        unSelectedIcon:
          $ref: "#/components/schemas/Widget"
        label:
          type: string
        link:
          $ref: "#/components/schemas/RedirectLink"
        tag:
          type: string
    BondDetails:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/BondDetailsProps"
        slots:
          $ref: "#/components/schemas/BondDetailsSlots"
    BondDetailsProps:
      type: object
      properties:
        title:
          type: string
        titleType:
          $ref: "#/components/schemas/TitleType"
        extendBodyBehindAppBar:
          type: boolean
        backgroundColor:
          type: string
        tags:
          type: array
          items:
            type: string
        name:
          type: string
        logo:
          type: string
        logoBg:
          type: string
        viewEventProps:
          type: object
    BondDetailsSlots:
      type: object
      properties:
        header:
          $ref: "#/components/schemas/Widget"
        children:
          type: array
          items:
            $ref: "#/components/schemas/Widget"
        heroHeader:
          $ref: "#/components/schemas/BondsHeroHeader"
        bottomButton:
          $ref: "#/components/schemas/Widget"
    BondsHeroHeader:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/BondsHeroHeaderProps"
        slots:
          $ref: "#/components/schemas/BondsHeroHeaderSlots"
    BondsHeroHeaderProps:
      type: object
      required:
        - isContentExpandable
      properties:
        isContentExpandable:
          type: boolean
        section:
          type: string
        bondId:
          type: string
        backgroundColor:
          type: string
    BondsHeroHeaderSlots:
      type: object
      properties:
        content:
          $ref: "#/components/schemas/Widget"
    BondsKeyHighlights:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/BondsKeyHighlightProps"
    BondsKeyHighlightProps:
      type: object
      required:
        - returns
        - tenure
        - soldUnits
      properties:
        availableUnitsPercent:
          type: number
          format: double
        returns:
          $ref: "#/components/schemas/TableDataItem"
        tenure:
          $ref: "#/components/schemas/TableDataItem"
        soldUnits:
          $ref: "#/components/schemas/TableDataItem"
    BondsMarketingHighlights:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/BondsMarketingHighlightsProps"
    BondsMarketingHighlightsProps:
      type: object
      properties:
        marketingHighlightsMap:
          type: array
          items:
            $ref: "#/components/schemas/DataKey"
    BondsInvestButton:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/BondsInvestButtonProps"
    BondsInvestButtonProps:
      type: object
      properties:
        label:
          type: string
        completeKYCLabel:
          type: string
        soldOutLabel:
          type: string
        isFloatingButton:
          type: boolean
        isKYCCompleted:
          type: boolean
        isBondSoldOut:
          type: boolean
        bondId:
          type: string
        analyticsEventProps:
          type: object
    BondsReturnsCalculator:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/BondsReturnsCalculatorProps"
    BondsReturnsCalculatorProps:
      type: object
      properties:
        title:
          type: string
        titleType:
          $ref: "#/components/schemas/TitleType"
        label:
          type: string
        initialUnits:
          type: integer
        pricePerBond:
          type: number
          format: double
        tenure:
          type: string
        rate:
          type: number
          format: double
        bondId:
          type: string
        payoutType:
          type: string
        issueDate:
          type: string
        maturityDate:
          type: string
        repaymentSchedule:
          type: array
          items:
            type: object
            properties:
              type:
                type: string
              interestAmount:
                type: number
                format: double
              principalAmount:
                type: number
                format: double
              date:
                type: string
              dateTime:
                type: string
        analyticsEventProps:
          type: object
    DataTable:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/DataTableProps"
    DataTableProps:
      type: object
      properties:
        title:
          type: string
        titleType:
          $ref: "#/components/schemas/TitleType"
        tableData:
          $ref: "#/components/schemas/TableDataResponse"
    BondsDataTable:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/DataTableProps"
    BondsDisclosureButton:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/BondsDisclosureButtonProps"
    BondsDisclosureButtonProps:
      type: object
      properties:
        title:
          type: string
        titleType:
          $ref: "#/components/schemas/TitleType"
        label:
          type: string
        bondId:
          type: string
        analyticsEventProps:
          type: object
    BondsDisclosureIssuerDetails:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/BondsDisclosureIssuerDetailsProps"
    BondsDisclosureIssuerDetailsProps:
      type: object
      properties:
        title:
          type: string
        titleType:
          $ref: "#/components/schemas/TitleType"
        bondName:
          type: string
        bondIssuerDescription:
          type: string
        bondLogo:
          type: string
        bondLogoBgColor:
          type: string
        tags:
          type: array
          items:
            type: string
        bondId:
          type: string
    BondsSupportedDocuments:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/BondsSupportedDocumentsProps"
    BondsSupportedDocumentsProps:
      type: object
      properties:
        title:
          type: string
        titleType:
          $ref: "#/components/schemas/TitleType"
        bondId:
          type: string
        supportedDocuments:
          type: array
          items:
            type: object
            properties:
              url:
                type: string
              name:
                type: string
    BondsDisclosureDetails:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/BondsDisclosureDetailsProps"
    BondsDisclosureDetailsProps:
      type: object
      properties:
        title:
          type: string
        titleType:
          $ref: "#/components/schemas/TitleType"
        bondId:
          type: string
        disclosures:
          $ref: "#/components/schemas/TableDataResponse"
        analyticsEventProps:
          type: object
    BondsSellerEntityDisclosure:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/BondsSellerEntityDisclosureProps"
    BondsSellerEntityDisclosureProps:
      type: object
      properties:
        title:
          type: string
        titleType:
          $ref: "#/components/schemas/TitleType"
        bondId:
          type: string
        sellerEntityDisclosure:
          type: string
    BondsFaqs:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/BondsFaqsProps"
    BondsFaqsProps:
      type: object
      properties:
        title:
          type: string
        titleType:
          $ref: "#/components/schemas/TitleType"
        label:
          type: string
    Card:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/CardProps"
        slots:
          $ref: "#/components/schemas/CardSlots"
    CardSlots:
      type: object
      properties:
        content:
          $ref: "#/components/schemas/Widget"
    CardProps:
      type: object
      properties:
        borderRadius:
          type: number
          format: double
        isAttachedWidget:
          type: boolean
        title:
          type: string
        isInnerPadding:
          type: boolean
        titleType:
          $ref: "#/components/schemas/TitleType"
        contentSpacingType:
          $ref: "#/components/schemas/CardContentSpacingType"
        contentRadiusType:
          $ref: "#/components/schemas/CardContentRadiusType"
    BondsCollection:
      type: object
      required:
        - id
        - props
        - widget
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/BondsCollectionProps"
    BondsCollectionProps:
      type: object
      required:
        - collectionName
        - cardType
      properties:
        collectionName:
          type: string
        cardType:
          $ref: "#/components/schemas/BondsCollectionCardType"
        viewAllMinLimit:
          type: integer
        indexed:
          type: boolean
        viewAllUrl:
          type: string
    BondsHeroCollection:
      type: object
      required:
        - id
        - props
        - widget
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/BondsHeroCollectionProps"
    BondsHeroCollectionProps:
      type: object
      required:
        - collectionName
      properties:
        collectionName:
          type: string
        viewAllMinLimit:
          type: integer
        indexed:
          type: boolean
    BondsExpandableCollection:
      type: object
      required:
        - id
        - props
        - widget
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/BondsExpandableCollectionProps"
    BondsExpandableCollectionProps:
      type: object
      required:
        - collectionName
      properties:
        collectionName:
          type: string
        viewAllMinLimit:
          type: integer
    BondsCollectionTable:
      type: object
      required:
        - id
        - props
        - widget
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/BondsCollectionTableProps"
    InsuranceAmountProgress:
      type: object
      required:
        - id
        - props
        - widget
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/InsuranceAmountProgressProps"
    InvestmentBasicDetails:
      type: object
      required:
        - id
        - props
        - widget
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/InvestmentCardProps"
    BondsCollectionTableProps:
      type: object
      required:
        - collectionName
        - columnHeadings
      properties:
        titleType:
          $ref: "#/components/schemas/TitleType"
        collectionName:
          type: string
        columnHeadings:
          type: array
          items:
            type: string
    Cta: # props item
      required:
        - text
        - redirectLink
      properties:
        text:
          type: string
        redirectLink:
          $ref: "#/components/schemas/RedirectLink"
      type: object
    InvestmentAmount: # props item
      required:
        - investedAmountTitleKey
        - interestEarnedTitleKey
        - currentAmountTitleKey
        - investedAmountValue
        - interestEarnedValue
        - currentAmountValue
      properties:
        investedAmountTitleKey:
          type: string
        interestEarnedTitleKey:
          type: string
        currentAmountTitleKey:
          type: string
        investedAmountValue:
          type: number
          format: double
        interestEarnedValue:
          type: number
          format: double
        currentAmountValue:
          type: number
          format: double
      type: object
    InsuranceAmountProgressProps: # props item
      required:
        - totalInsuredAmount
        - currentAmount
        - subTitle
      properties:
        totalInsuredAmount:
          type: number
          format: double
        currentAmount:
          type: number
          format: double
        subTitle:
          type: string
        footerText:
          type: string
        cta:
          $ref: "#/components/schemas/Cta"
      type: object
    OrgInvestmentSummaryProps: # props item
      properties:
        investmentAmountSummary:
          $ref: "#/components/schemas/InvestmentAmount"
        insuranceAmountProgress:
          $ref: "#/components/schemas/InsuranceAmountProgressProps"
        footerText:
          type: string
        nudgeId:
          type: string
        isRootWidget:
          type: boolean
      type: object
    OrgInvestmentSummary: # widget
      properties:
        id:
          type: string
        props:
          $ref: "#/components/schemas/OrgInvestmentSummaryProps"
        widget:
          type: string
      required:
        - widget
        - id
        - props
      type: object
    OrgInvestmentCards: # widget
      properties:
        id:
          type: string
        props:
          $ref: "#/components/schemas/OrgInvestmentCardsProps"
        widget:
          type: string
      required:
        - widget
        - id
        - props
      type: object
    OrgInvestmentCardsProps:
      type: object # props
      properties:
        headerText:
          type: string
        cards:
          type: array
          items:
            $ref: "#/components/schemas/OrgInvestmentCard"
    OrgInvestmentCard: # props item
      required:
        - orgInvestmentStatus
        - isCollapsed
        - title
        - subTitle
        - orgColor
      properties:
        orgInvestmentStatus:
          $ref: "#/components/schemas/InvestmentStatus"
        isCollapsed:
          type: boolean
        title:
          type: string
        orgIconUrls:
          type: array
          items:
            type: string
        orgColor:
          type: string
        subTitle:
          type: string
        animateOnRedirect:
          type: boolean
        investmentSummary:
          $ref: "#/components/schemas/InvestmentAmount"
        redirectLink:
          $ref: "#/components/schemas/RedirectLink"
        productType:
          $ref: "#/components/schemas/ProductType"
      type: object
    InvestmentCards: # widget
      properties:
        id:
          type: string
        props:
          $ref: "#/components/schemas/InvestmentCardsProps"
        widget:
          type: string
      required:
        - widget
        - id
        - props
      type: object
    InvestmentCardsProps: # props
      type: object
      properties:
        cards:
          type: array
          items:
            $ref: "#/components/schemas/InvestmentCardProps"
    InvestmentCardProps: # props item
      properties:
        investmentStatus:
          $ref: "#/components/schemas/InvestmentStatus"
        title:
          type: string
        bankIconUrls:
          type: array
          items:
            type: string
        bankColor:
          type: string
        footerText:
          type: string
        insuranceAmountProgress:
          $ref: "#/components/schemas/InsuranceAmountProgressProps"
        subTitle:
          type: string
        investmentSummary:
          $ref: "#/components/schemas/InvestmentAmount"
        redirectLink:
          $ref: "#/components/schemas/RedirectLink"
        productType:
          $ref: "#/components/schemas/ProductType"
      type: object
    InvestmentStatus: # enum for investment status
      type: string
      enum: [ACTIVE, PROCESSING, MATURED, WITHDRAWN]
    ProductType: # enum for various products
      type: string
      enum: [FD, BOND]
    BondsGridCollection: # widget
      type: object
      required:
        - widget
        - id
        - props
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/BondsGridCollectionProps"
    BondsGridCollectionProps: # props
      type: object
      required:
        - collectionName
      properties:
        collectionName:
          type: string
        backgroundColor:
          type: string
    KebabMenuV2: # widget
      properties:
        id:
          type: string
        slots:
          $ref: "#/components/schemas/KebabMenuV2Slots"
        props:
          $ref: "#/components/schemas/KebabMenuV2Props"
        widget:
          type: string
      type: object
    KebabMenuV2Slots:
      type: object
      properties:
        iconWidget:
          $ref: "#/components/schemas/Widget"
        menuItems:
          type: array
          items:
            $ref: "#/components/schemas/MenuItemV2"
    KebabMenuV3: # widget
      properties:
        id:
          type: string
        slots:
          $ref: "#/components/schemas/KebabMenuV3Slots"
        props:
          $ref: "#/components/schemas/KebabMenuV3Props"
        widget:
          type: string
      type: object
    KebabMenuV3Slots:
      type: object
      properties:
        iconWidget:
          $ref: "#/components/schemas/Widget"
        menuItems:
          type: array
          items:
            $ref: "#/components/schemas/Widget"
    KebabMenuV3Props: #props item
      type: object
      properties:
        label:
          type: string
    FDReceiptKebabMenuItem: # widget
      properties:
        id:
          type: string
        props:
          $ref: "#/components/schemas/FDReceiptKebabMenuItemProps"
        slots:
          $ref: "#/components/schemas/FDReceiptKebabMenuItemSlots"
        widget:
          type: string
      type: object
    FDReceiptKebabMenuItemProps: #props item
      type: object
      properties:
        label:
          $ref: "#/components/schemas/DataKey"
        disabledToastText:
          type: string
        redirectLink:
          $ref: "#/components/schemas/RedirectLink"
        data:
          type: object
          additionalProperties: true
    FDReceiptKebabMenuItemSlots:
      type: object
      properties:
        leadingWidget:
          $ref: "#/components/schemas/Widget"
    MenuItemV2: # widget
      properties:
        id:
          type: string
        props:
          $ref: "#/components/schemas/MenuItemV2Props"
        slots:
          $ref: "#/components/schemas/MenuItemV2Slots"
        widget:
          type: string
      type: object
    KebabMenuV2Props: #props item
      type: object
      properties:
        label:
          type: string
    MenuItemV2Slots:
      type: object
      properties:
        leadingWidget:
          $ref: "#/components/schemas/Widget"
    MenuItemV2Props: #props item
      type: object
      properties:
        label:
          $ref: "#/components/schemas/DataKey"
        disabledToastText:
          type: string
        redirectLink:
          $ref: "#/components/schemas/RedirectLink"
    KebabMenu: # widget
      properties:
        id:
          type: string
        props:
          $ref: "#/components/schemas/KebabMenuProps"
        widget:
          type: string
      required:
        - widget
        - id
        - props
      type: object
    BondsTnCAndKYCFooter: # widget
      properties:
        id:
          type: string
        props:
          $ref: "#/components/schemas/BondsTnCAndKYCFooterProps"
        widget:
          type: string
      required:
        - widget
        - id
        - props
      type: object
    BondsTnCAndKYCFooterProps: # props
      type: object
      properties:
        isKYCCompleted:
          type: boolean
    BondsSingleMedia:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/BondsSingleMediaProps"
        slots:
          $ref: "#/components/schemas/BondsSingleMediaSlots"
    BondsSingleMediaProps:
      type: object
      required:
        - section
      properties:
        title:
          type: string
        titleType:
          $ref: "#/components/schemas/TitleType"
        bondId:
          type: string
        section:
          type: string
    BondsSingleMediaSlots:
      type: object
      properties:
        content:
          $ref: "#/components/schemas/Widget"
    KebabMenuProps: # props
      type: object
      required:
        - image
      properties:
        iconUrl:
          type: string
        menuItems:
          type: array
          items:
            $ref: "#/components/schemas/MenuItem"
    MenuItem: #props item
      type: object
      properties:
        iconUrl:
          type: string
        label:
          type: string
        redirectLink:
          $ref: "#/components/schemas/RedirectLink"
      required:
        - label
        - redirectLink
    InAppRatingInvisibleWidget:
      type: object
      properties:
        widget:
          type: string
        id:
          type: string
    # Suryoday credit card
    SuryodayCreditCardFooter:
      type: object
      required:
        - widget
        - id
        - props
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/SuryodayCreditCardFooterProps"
    SuryodayCreditCardFooterProps:
      type: object
      required:
        - ctaWidget
      properties:
        ctaWidget:
          $ref: "#/components/schemas/Widget"
    RedirectWidget:
      type: object
      required:
        - widget
        - id
        - props
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/RedirectWidgetProps"
    RedirectWidgetProps:
      type: object
      required:
        - link
      properties:
        link:
          $ref: "#/components/schemas/RedirectLink"
    FDCollectionTable:
      type: object
      required:
        - id
        - props
        - widget
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/FDCollectionTableProps"
    FDCollectionTableProps:
      type: object
      required:
        - columnHeadings
      properties:
        titleType:
          $ref: "#/components/schemas/TitleType"
        collectionId:
          type: string
          deprecated: true
        collectionName:
          type: string
        columnHeadings:
          type: array
          items:
            type: string

    # Bank Details
    BankDetails:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/BankDetailsProps"
        slots:
          $ref: "#/components/schemas/BankDetailsSlots"
    BankDetailsProps:
      type: object
      properties:
        title:
          type: string
        titleType:
          $ref: "#/components/schemas/TitleType"
        extendBodyBehindAppBar:
          type: boolean
        backgroundColor:
          type: string
        tags:
          type: array
          items:
            type: string
        name:
          type: string
        logo:
          type: string
        logoBg:
          type: string
        branchLocatorImageUrl:
          type: string
        viewEventProps:
          type: object
        logoFrameRedirectLink:
          $ref: "#/components/schemas/RedirectLink"
    BankDetailsSlots:
      type: object
      properties:
        header:
          $ref: "#/components/schemas/Widget"
        children:
          type: array
          items:
            $ref: "#/components/schemas/Widget"
        heroHeader:
          $ref: "#/components/schemas/BanksHeroHeader"
        bottomButton:
          $ref: "#/components/schemas/Widget"
        logoFrame:
          $ref: "#/components/schemas/Widget"
    BanksHeroHeader:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/BanksHeroHeaderProps"
        slots:
          $ref: "#/components/schemas/BanksHeroHeaderSlots"
    BanksHeroHeaderProps:
      type: object
      required:
        - isContentExpandable
      properties:
        isContentExpandable:
          type: boolean
        section:
          type: string
        bankId:
          type: string
        backgroundColor:
          type: string
    BanksHeroHeaderSlots:
      type: object
      properties:
        content:
          $ref: "#/components/schemas/Widget"

    BankTableCard:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/BankTableCardProps"
        slots:
          $ref: "#/components/schemas/BankTableCardSlots"

    BankTableCardProps:
      type: object
      properties:
        section:
          type: string
        tags:
          type: array
          items:
            type: string
        title:
          $ref: "#/components/schemas/DataKey"
        bankResponse:
          $ref: "#/components/schemas/BankResponseProps"
        footerText:
          $ref: "#/components/schemas/DataKey"

    BankTableCardSlots:
      type: object
      properties:
        children:
          type: array
          items:
            $ref: "#/components/schemas/BankTableCardItem"

    BankTableCardItem:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/BankTableCardItemProps"

    BankTableCardItemProps:
      type: object
      properties:
        label:
          type: string
        labelValue:
          type: string
        subLabel:
          type: string

    BankIbCompareCard:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/BankIbCompareCardProps"

    BankIbCompareCardProps:
      type: object
      properties:
        title:
          $ref: "#/components/schemas/DataKey"
        note:
          $ref: "#/components/schemas/DataKey"
        children:
          type: array
          items:
            $ref: "#/components/schemas/BankIbCompareCardItem"

    BankIbCompareCardItem:
      type: object
      properties:
        interestRate:
          type: number
          format: double
        bankLogoUrl:
          type: string
        bankName:
          type: string

    BankTenureCard:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/BankTenureCardProps"
        slots:
          $ref: "#/components/schemas/BanksTenureCardSlots"
    BankDICGCCard:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/BankDICGCCardProps"
    BankDICGCCardProps:
      type: object
      properties:
        backgroundImageUrl:
          type: string
        bankId:
          type: string
        bankName:
          type: string
        heading:
          type: string
        profileImageUrls:
          type: array
          items:
            type: string
    BanksTenureCardSlots:
      type: object
      properties:
        content:
          $ref: "#/components/schemas/Widget"

    BankTenureCardProps:
      type: object
      properties:
        children:
          type: array
          items:
            $ref: "#/components/schemas/BankTenureCardItems"
        womenRoiDiff:
          type: number
          format: double
        childrenWomenRates:
          type: array
          items:
            $ref: "#/components/schemas/BankTenureCardItems"
        tenureBts:
          $ref: "#/components/schemas/BankTenureCardBottomsheetProps"
        bankResponse:
          $ref: "#/components/schemas/BankResponseProps"
        childrenV2:
          type: array
          items:
            $ref: "#/components/schemas/BankTenureCardItems"
        childrenWomenRatesV2:
          type: array
          items:
            $ref: "#/components/schemas/BankTenureCardItems"
        xirrText:
          $ref: "#/components/schemas/DataKey"
        lineItemIconUrl:
          type: string
        bottomsheetLineItemIconUrl:
          type: string

    BankTenureCardItems:
      type: object
      properties:
        tenure:
          type: string
        investorTypeLabel:
          type: string
        tag:
          type: string
        interestRate:
          type: number
          format: double
        xirr:
          type: number
          format: double

    BankTenureCardBottomsheetProps:
      type: object
      properties:
        bankResponse:
          $ref: "#/components/schemas/BankResponseProps"
        description:
          type: string
        womenRoiDiff:
          type: number
          format: double
        isRateChangeNotificationEnabled:
          type: boolean
        tenureLineItems:
          type: array
          items:
            $ref: "#/components/schemas/BankTenureCardBottomsheetItems"
        womenTenureLineItems:
          type: array
          items:
            $ref: "#/components/schemas/BankTenureCardBottomsheetItems"
        seniorCitizenRoiDiff:
          type: number
          format: double
        tenureLineItemsGeneral:
          type: array
          items:
            $ref: "#/components/schemas/BankTenureCardBottomsheetItemsV2"
        tenureLineItemsSenior:
          type: array
          items:
            $ref: "#/components/schemas/BankTenureCardBottomsheetItemsV2"
        tenureLineItemsWomen:
          type: array
          items:
            $ref: "#/components/schemas/BankTenureCardBottomsheetItemsV2"
        tenureLineItemsWomenSenior:
          type: array
          items:
            $ref: "#/components/schemas/BankTenureCardBottomsheetItemsV2"

    BankTenureCardBottomsheetItems:
      type: object
      properties:
        tenure:
          type: string
        tag:
          type: string
        generalInterestRate:
          type: number
          format: double
        seniorInterestRate:
          type: number
          format: double
    BankTenureCardBottomsheetItemsV2:
      type: object
      properties:
        tenure:
          type: string
        tag:
          type: string
        rate:
          type: number
          format: double
        xirr:
          type: number
          format: double

    BankFdNibCompareCard:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/BankFdNibCompareCardProps"

    BankFdNibCompareCardProps:
      type: object
      required:
        - highestRateBankResponse
        - bankResponse
        - compareLineItems
      properties:
        title:
          type: string
        primaryCtaText:
          type: string
        highestRateBankResponse:
          $ref: "#/components/schemas/BankResponseProps"
        bankResponse:
          $ref: "#/components/schemas/BankResponseProps"
        compareLineItems:
          type: array
          items:
            $ref: "#/components/schemas/BankFdNibCompareCardLineItemsProps"

    BankResponseProps:
      type: object
      required:
        - bankName
        - bankLogoUrl
        - bankId
        - investabilityStatus
      properties:
        bankName:
          type: string
        bankLogoUrl:
          type: string
        bankId:
          type: string
        bankColor:
          type: string
        investabilityStatus:
          $ref: "#/components/schemas/BankInvestabilityStatus"

    BankFdNibCompareCardLineItemsProps:
      type: object
      properties:
        title:
          type: string
        highestBankPoint:
          type: string
        currentBankPoint:
          type: string
        moreDetailsLink:
          $ref: "#/components/schemas/RedirectLink"

    BankMessageWidget:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/BankMessageWidgetProps"

    BankMessageWidgetProps:
      type: object
      properties:
        message:
          $ref: "#/components/schemas/DataKey"

    BankNibInvestableOptions:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/BankNibInvestableOptionsProps"

    BankNibInvestableOptionsProps:
      type: object
      properties:
        title:
          $ref: "#/components/schemas/DataKey"
        description:
          $ref: "#/components/schemas/DataKey"
        collectionId:
          type: string
          deprecated: true
        collectionName:
          type: string
        itemCtaText:
          type: string
        itemComingSoonText:
          type: string

    BankShareWidget:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/BankShareWidgetProps"

    BankShareWidgetProps:
      type: object
      required:
        - isShareEnabled
      properties:
        shareText:
          $ref: "#/components/schemas/DataKey"
        shareImageUrl:
          type: string
        isShareEnabled:
          type: boolean
        bankResponse:
          $ref: "#/components/schemas/BankResponseProps"
    BankInvestButton:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/BankInvestButtonProps"
    BankInvestButtonProps:
      type: object
      required:
        - primaryButton
        - bankId
      properties:
        isFloating:
          type: boolean
        bankId:
          type: string
        primaryButton:
          $ref: "#/components/schemas/BankButtonProps"
        secondaryButton:
          $ref: "#/components/schemas/BankButtonProps"
        bankResponse:
          $ref: "#/components/schemas/BankResponseProps"
    BankButtonProps:
      type: object
      required:
        - ctaText
      properties:
        ctaText:
          type: string
        showSecondaryButtonUi:
          type: boolean
        mapFdWithEf:
          type: boolean
        redirectLink:
          $ref: "#/components/schemas/RedirectLink"
        tag:
          $ref: "#/components/schemas/BankButtonTagProps"
    BankButtonTagProps:
      type: object
      properties:
        text:
          type: string
        textColor:
          type: string
        backgroundColor:
          type: string
        shimmerColor:
          type: string
        borderColor:
          type: string
        disableShimmer:
          type: boolean
        child:
          $ref: "#/components/schemas/Widget"
        prefixIcon:
          $ref: "#/components/schemas/Widget"

    BanksSingleMedia:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/BanksSingleMediaProps"
        slots:
          $ref: "#/components/schemas/BanksSingleMediaSlots"
    BanksSingleMediaProps:
      type: object
      required:
        - section
      properties:
        title:
          type: string
        titleType:
          $ref: "#/components/schemas/TitleType"
        section:
          type: string
        analyticsEventProps:
          type: object
    BanksSingleMediaSlots:
      type: object
      properties:
        content:
          $ref: "#/components/schemas/Widget"

    BankHighlightPointersCard:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/BankHighlightPointersCardProps"
    BankHighlightPointersCardProps:
      type: object
      properties:
        section:
          type: string
        tags:
          type: array
          items:
            type: string
        title:
          $ref: "#/components/schemas/DataKey"
        children:
          type: array
          items:
            $ref: "#/components/schemas/BankHighlightPointersCardItem"
    BankHighlightPointersCardItem:
      type: object
      properties:
        text:
          $ref: "#/components/schemas/DataKey"

    BankWithdrawalCalculatorCard:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/BankWithdrawalCalculatorCardProps"
    SuryodayCreditCardAddressChangeFooter:
      type: object
      required:
        - widget
        - id
        - props
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/SuryodayCreditCardAddressChangeFooterProps"
    SuryodayCreditCardAddressChangeFooterProps:
      type: object
      required:
        - ctaType
      properties:
        ctaType:
          type: string
          enum: [SUCCESS, FAILED, IN_PROGRESS]
        ctaText:
          type: string
    BankWithdrawalCalculatorCardProps:
      type: object
      properties:
        title:
          $ref: "#/components/schemas/DataKey"
        cardTitle:
          $ref: "#/components/schemas/DataKey"
        returnsDescription:
          $ref: "#/components/schemas/DataKey"
        zeroReturnsDescription:
          $ref: "#/components/schemas/DataKey"
        intermediateZeroReturnsDescription:
          $ref: "#/components/schemas/DataKey"
        footerText:
          $ref: "#/components/schemas/DataKey"
        fdWithdrawalCalculationDetails:
          $ref: "#/components/schemas/FdWithdrawalCalculationDetailsProps"
        bankResponse:
          $ref: "#/components/schemas/BankResponseProps"

    FdWithdrawalCalculationDetailsProps:
      type: object
      properties:
        interestRate:
          type: number
          format: double
        investmentAmount:
          type: number
          format: double
        maturityAmount:
          type: number
          format: double
        tenure:
          type: string
        sliderPoints:
          type: array
          items:
            $ref: "#/components/schemas/FdWithdrawalCalculationItem"
        bottomsheetData:
          $ref: "#/components/schemas/FdWithdrawalCalculationBtsData"
    FdWithdrawalCalculationItem:
      type: object
      properties:
        tenure:
          type: string
        withdrawalAmount:
          type: number
          format: double
        rate:
          type: number
          format: double
    FdWithdrawalCalculationBtsData:
      type: object
      properties:
        title:
          $ref: "#/components/schemas/DataKey"
        description:
          $ref: "#/components/schemas/DataKey"
        children:
          type: array
          items:
            $ref: "#/components/schemas/FdWithdrawalCalculationBtsDataItems"
    FdWithdrawalCalculationBtsDataItems:
      type: object
      properties:
        tenure:
          type: string
        rate:
          type: number
          format: double
        originalRate:
          type: number
          format: double

    DynamicBankFaqs:
      type: object
      required:
        - widget
        - id
        - props
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/DynamicBankFaqsProps"
    DynamicBankFaqsProps:
      type: object
      required:
        - faqCategory
      properties:
        faqBusinessUnit:
          type: string
        faqCategory:
          type: string
        faqNamespaceIdentifier:
          type: string
        title:
          type: string
        titleType:
          $ref: "#/components/schemas/TitleType"
        maxCollapsedCount:
          type: integer
        maxViewAllFAQLabel:
          type: string
        expandFAQByDefault:
          type: boolean
        redirectLink:
          $ref: "#/components/schemas/RedirectLink"
        bankResponse:
          $ref: "#/components/schemas/BankResponseProps"

    SuryodayCreditCardWaitListFooter:
      type: object
      required:
        - widget
        - id
        - props
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/SuryodayCreditCardWaitListProps"
    SuryodayCreditCardWaitListProps:
      type: object
      required:
        - notifyCTAWidget
        - inviteCTAWidget
        - notifyMeText
        - shareText
      properties:
        notifyCTAWidget:
          $ref: "#/components/schemas/Widget"
        inviteCTAWidget:
          $ref: "#/components/schemas/Widget"
        notifyMeText:
          type: string
        shareText:
          type: string
    #          POLL Response
    WidgetInteractionResponse:
      type: object
      properties:
        totalInteractionCount:
          type: string
        totalInteractionCountShort:
          type: string
        lastUserName:
          type: string
        lastInteractionTimeAgo:
          type: string
    PollResponse:
      type: object
      properties:
        pollId:
          type: string
        title:
          type: string
        isDismissible:
          type: boolean
        hideWhenCompleted:
          type: boolean
        autoRedirect:
          type: boolean
        redirectLink:
          $ref: "#/components/schemas/RedirectLink"
        questions:
          type: array
          items:
            $ref: "#/components/schemas/PollQuestion"
    PollQuestion:
      type: object
      properties:
        questionId:
          type: string
        question:
          type: string
        description:
          type: string
        questionType:
          type: string
          enum:
            [
              SINGLE_SELECTION,
              MULTIPLE_SELECTION,
              CORRECT_ANSWER_SELECTION,
              RANGE_SELECTION,
            ]
        options:
          type: array
          items:
            $ref: "#/components/schemas/PollQuestionOption"
        shuffleOptions:
          type: boolean
        isQuestionAnswered:
          type: boolean
        config:
          $ref: "#/components/schemas/PollConfig"
    PollConfig:
      type: object
      properties:
        lowerRangeLabel:
          type: string
        higherRangeLabel:
          type: string
        rangeFeedbackTitle:
          type: string
        rangeFeedbackDescription:
          type: string
        rangeFeedbackInputHint:
          type: string
    PollQuestionOption:
      type: object
      properties:
        optionId:
          type: string
        text:
          type: string
        percentage:
          type: number
          format: double
        isCorrectAnswer:
          type: boolean
        isSelected:
          type: boolean
    PollQuestionSubmitRequest:
      type: object
      properties:
        selectedOptions:
          type: array
          items:
            type: string

    PollCommentSubmitRequest:
      type: object
      required:
        - comment
      properties:
        comment:
          type: string

    PollOptionsJsonParser:
      type: object
      properties:
        options:
          type: array
          items:
            $ref: "#/components/schemas/PollQuestionOption"

    PollResponseContentJsonParser:
      type: object
      properties:
        redirectLink:
          $ref: "#/components/schemas/RedirectLink"
    Text:
      type: object
      required:
        - widget
        - id
        - props
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/TextProps"
    TextProps:
      type: object
      required:
        - text
      properties:
        text:
          $ref: "#/components/schemas/DataKey"
        textStyle:
          $ref: "#/components/schemas/TextStyle"
        isCentreAligned:
          type: boolean
    Counter:
      type: object
      required:
        - widget
        - id
        - props
        - slots
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/CounterProps"
        slots:
          $ref: "#/components/schemas/CounterSlots"
    CounterProps:
      type: object
      required:
        - endTime
      properties:
        endTime:
          type: string
    CounterSlots:
      type: object
      required:
        - content
      properties:
        content:
          $ref: "#/components/schemas/Widget"

    PollCard:
      type: object
      required:
        - widget
        - id
        - props
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/PollCardProps"
    PollCardProps:
      type: object
      required:
        - pollId
      properties:
        pollId:
          type: string

    PollQuestionWithUserSelections:
      type: object
      properties:
        questionId:
          type: string
        question:
          type: string
        description:
          type: string
        questionType:
          type: string
        optionsJson:
          type: string
        shuffleOptions:
          type: boolean
        selectedOptions:
          type: array
          items:
            type: string
        configJson:
          type: string
    HorizontalCollectionSection:
      type: object
      required:
        - widget
        - id
        - props
        - slots
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/HorizontalCollectionSectionProps"
        slots:
          $ref: "#/components/schemas/HorizontalCollectionSectionSlots"
    HorizontalCollectionSectionProps:
      type: object
      properties:
        title:
          type: string
        titleType:
          $ref: "#/components/schemas/TitleType"
    HorizontalCollectionSectionSlots:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: "#/components/schemas/HorizontalCollectionItem"
    HorizontalCollectionItem:
      type: object
      required:
        - id
        - widget
        - props
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/HorizontalCollectionItemProps"
    HorizontalCollectionItemProps:
      type: object
      properties:
        icon:
          $ref: "#/components/schemas/Widget"
        collectionName:
          type: string
        title:
          type: string
    ProfileHeader:
      type: object
      required:
        - id
        - widget
      properties:
        widget:
          type: string
        id:
          type: string
    ProfileMenuItem:
      type: object
      required:
        - id
        - widget
        - props
        - slots
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/ProfileMenuItemProps"
        slots:
          $ref: "#/components/schemas/ProfileMenuItemSlots"
    ProfileMenuItemSlots:
      type: object
      required:
        - icon
      properties:
        icon:
          $ref: "#/components/schemas/Widget"
        tag:
          $ref: "#/components/schemas/Widget"
    ProfileMenuItemProps:
      type: object
      required:
        - title
      properties:
        title:
          type: string
        redirectLink:
          $ref: "#/components/schemas/RedirectLink"
    ProfileFooter:
      type: object
      required:
        - widget
        - id
        - props
        - slots
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/ProfileFooterProps"
        slots:
          $ref: "#/components/schemas/ProfileFooterSlots"
    ProfileFooterSlots:
      type: object
      required:
        - logo
        - disclaimer
      properties:
        logo:
          $ref: "#/components/schemas/Widget"
    ProfileFooterProps:
      type: object
      required:
        - links
      properties:
        links:
          type: array
          items:
            $ref: "#/components/schemas/FooterLink"
        disclaimer:
          $ref: "#/components/schemas/DataKey"
    FooterLink:
      type: object
      required:
        - text
        - redirectLink
      properties:
        text:
          type: string
        redirectLink:
          $ref: "#/components/schemas/RedirectLink"
    TabularPage:
      type: object
      required:
        - widget
        - id
        - props
        - slots
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/TabularPageProps"
        slots:
          $ref: "#/components/schemas/TabularPageSlots"
    TabularPageSlots:
      type: object
      properties:
        header:
          $ref: "#/components/schemas/Widget"
    TabularPageProps:
      type: object
      required:
        - tabs
      properties:
        selectedTabId:
          type: string
        tabs:
          type: array
          items:
            $ref: "#/components/schemas/TabItem"
        viewEventProps:
          type: object
        swipeable:
          type: boolean
          description: >
            If true, allows switching between tabs using swipe gestures.
            Defaults to true for backward compatibility.
          default: true
        keepAlive:
          type: boolean
          description: >
            If true, all tabs are kept alive and rendered using an IndexedStack approach.
            Defaults to false for backward compatibility.
          default: false
    RegularTabItem:
      type: object
      required:
        - id
        - label
        - path
        - tabItem
      properties:
        id:
          type: string
        label:
          type: string
        path:
          type: string
        tabItem:
          type: string
    TaggedTabItem:
      type: object
      required:
        - id
        - label
        - path
        - tag
        - tabItem
      properties:
        id:
          type: string
        label:
          type: string
        tag:
          type: string
        path:
          type: string
        tabItem:
          type: string
        analyticsEventProps:
          type: object
    TranslucentTag:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/TranslucentTagProps"
        slots:
          $ref: "#/components/schemas/TranslucentTagSlots"
    TranslucentTagSlots:
      type: object
      properties:
        prefixIcon:
          $ref: "#/components/schemas/Widget"
    TranslucentTagProps:
      type: object
      properties:
        label:
          $ref: "#/components/schemas/DataKey"
        color:
          type: string
        hasShimmer:
          type: boolean
    SolidTag:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/SolidTagProps"
        slots:
          $ref: "#/components/schemas/SolidTagSlots"
    SolidTagSlots:
      type: object
      properties:
        prefixIcon:
          $ref: "#/components/schemas/Widget"
    SolidTagProps:
      type: object
      properties:
        label:
          $ref: "#/components/schemas/DataKey"
        color:
          type: string
    DefaultWidget:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
    DefaultTabItem:
      type: object
      required:
        - id
        - tabItem
      properties:
        id:
          type: string
        tabItem:
          type: string
    TrustMarkersFooter:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/TrustMarkersFooterProps"

    TrustMarkersFooterProps:
      type: object
      properties:
        title:
          type: string
        subTitle:
          type: string
        subHeader:
          type: string
        items:
          type: array
          items:
            $ref: "#/components/schemas/TrustMarkersFooterBankItem"
        subFooter:
          type: string
        footerText:
          type: string
        footerTextPrefixIcon:
          type: string
        footerRedirect:
          $ref: "#/components/schemas/RedirectLink"
    TrustMarkersFooterBankItem:
      type: object
      properties:
        iconUrl:
          type: string
        redirect:
          $ref: "#/components/schemas/RedirectLink"
    FDSearchAndFilter:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/FDSearchAndFilterProps"
    FDSearchAndFilterProps:
      type: object
      properties:
        sortItems:
          type: array
          items:
            $ref: "#/components/schemas/SortItem"
        filterItems:
          type: array
          items:
            $ref: "#/components/schemas/FilterItem"
        quickFilterItems:
          type: array
          items:
            $ref: "#/components/schemas/FilterItem"
        interestRateFormat:
          type: array
          items:
            $ref: "#/components/schemas/InterestRateFormat"
    BondsSearchAndFilter:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/BondsSearchAndFilterProps"
    BondsSearchAndFilterProps:
      type: object
      properties:
        redirectionUrl:
          type: string
        sortItems:
          type: array
          items:
            $ref: "#/components/schemas/SortItem"
        filterItems:
          type: array
          items:
            $ref: "#/components/schemas/FilterItem"
        quickFilterItems:
          type: array
          items:
            $ref: "#/components/schemas/FilterItem"
    SortItem:
      type: object
      properties:
        sortType:
          type: string
        priority:
          type: integer
    FilterItem:
      type: object
      properties:
        key:
          type: string
        priority:
          type: integer
    InterestRateFormat:
      type: object
      properties:
        formatType:
          type: string
        priority:
          type: integer
        isDefault:
          type: boolean
    FDRewardOptInWidget:
      type: object
      required:
        - widget
        - id
      properties:
        widget:
          type: string
        id:
          type: string
    UnitySavingAccountCreateFooter:
      type: object
      required:
        - widget
        - id
        - props
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/UnitySavingAccountCreateFooterProps"
    UnitySavingAccountCreateFooterProps:
      type: object
      required:
        - ctaWidget
      properties:
        ctaWidget:
          $ref: "#/components/schemas/Widget"
        bankId:
          type: string
    UnitySavingAccountBankEntryPoint:
      type: object
      required:
        - widget
        - id
        - props
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/UnitySavingAccountBankEntryPointProps"
    UnitySavingAccountBankEntryPointProps:
      type: object
      properties:
        activeAccountWidget:
          $ref: "#/components/schemas/Widget"
        eligibleAccountWidget:
          $ref: "#/components/schemas/Widget"
        bankId:
          type: string
    UnitySavingAccountInterestRateTable:
      type: object
      required:
        - widget
        - id
        - props
      properties:
        widget:
          type: string
        id:
          type: string
        props:
          $ref: "#/components/schemas/UnitySavingAccountInterestRateTableProps"
    UnitySavingAccountInterestRateTableProps:
      type: object
      properties:
        bankId:
          type: string
