import { useMemo, type HTMLAttributes } from "react";
import Card from "./card";
import ProgressBar from "@/components/ui/progress-bar/progress-bar";
import useDataKey from "@/hooks/data-key";
import HorizontalList from "@/components/ui/horizontal-list/horizontal-list";
import HorizontalListItem from "@/components/ui/horizontal-list/horizontal-list-item";
import RichText from "@/components/ui/rich-text/rich-text";
import type { BondsKeyHighlightProps } from "@/clients/gen/personalization_api";
import AdaptiveModal from "@/components/ui/adaptive-modal/adaptive-modal";
import BondsTooltipPage from "@/pages/sheet/components/bonds-tool-tip";

type BondsKeyHighlightsComponentProps = Omit<
  BondsKeyHighlightProps,
  "returns" | "tenure" | "soldUnits"
> & {
  returns?: BondsKeyHighlightProps["returns"];
  tenure?: BondsKeyHighlightProps["tenure"];
  soldUnits?: BondsKeyHighlightProps["soldUnits"];
} & HTMLAttributes<HTMLDivElement>;

export default function BondsKeyHighlights({
  returns,
  tenure,
  soldUnits,
  availableUnitsPercent = 0,
  ...rest
}: BondsKeyHighlightsComponentProps) {
  const returnsLabel = useDataKey(returns?.labelDataKey);
  const returnsValue = useDataKey(returns?.valueDataKey);
  const tenureLabel = useDataKey(tenure?.labelDataKey);
  const tenureValue = useDataKey(tenure?.valueDataKey);
  const soldUnitsLabel = useDataKey(soldUnits?.labelDataKey);
  const soldUnitsValue = useDataKey(soldUnits?.valueDataKey);

  // Function to determine progress bar color based on available units percentage
  const getSoldProgress = useMemo(
    () =>
      (progress: number): string => {
        if (progress < 50) {
          return "#12be57"; // Green
        } else if (progress >= 50 && progress < 80) {
          return "#F47126"; // Orange
        } else if (progress >= 80) {
          return "#EE4950"; // Red
        } else {
          return "#D9D9D9"; // Gray
        }
      },
    []
  );

  return (
    <div className="mb-3 px-5" data-widget="BondsKeyHighlights" {...rest}>
      <Card>
        <HorizontalList className="divide-x">
          {returns?.labelDataKey && returns?.valueDataKey && (
            <AdaptiveModal
              size="small"
              trigger={
                <HorizontalListItem
                  className="cursor-pointer"
                  label={
                    <span className="flex items-center gap-1">
                      {returnsLabel}
                      <svg
                        width="12"
                        height="12"
                        viewBox="0 0 12 12"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <rect
                          width="12"
                          height="12"
                          rx="3"
                          fill="black"
                          fillOpacity="0.05"
                        />
                        <rect
                          x="0.2"
                          y="0.2"
                          width="11.6"
                          height="11.6"
                          rx="2.8"
                          stroke="black"
                          strokeOpacity="0.15"
                          strokeWidth="0.4"
                        />
                        <path
                          opacity="0.6"
                          d="M5.89266 4.73438H5.35648C5.21428 4.73438 5.07791 4.79242 4.97735 4.89573C4.8768 4.99905 4.82031 5.13918 4.82031 5.28529C4.82031 5.4314 4.8768 5.57153 4.97735 5.67484C5.07791 5.77816 5.21428 5.8362 5.35648 5.8362H5.89266V9.14169C5.89266 9.2878 5.94915 9.42793 6.0497 9.53124C6.15025 9.63456 6.28663 9.6926 6.42883 9.6926C6.57103 9.6926 6.70741 9.63456 6.80796 9.53124C6.90851 9.42793 6.965 9.2878 6.965 9.14169V5.8362C6.965 5.54398 6.85202 5.26373 6.65092 5.05709C6.44981 4.85046 6.17706 4.73438 5.89266 4.73438Z"
                          fill="black"
                        />
                        <path
                          opacity="0.6"
                          d="M5.8941 3.63809C6.33828 3.63809 6.69836 3.26811 6.69836 2.81172C6.69836 2.35533 6.33828 1.98535 5.8941 1.98535C5.44992 1.98535 5.08984 2.35533 5.08984 2.81172C5.08984 3.26811 5.44992 3.63809 5.8941 3.63809Z"
                          fill="black"
                        />
                      </svg>
                    </span>
                  }
                  value={<RichText text={returnsValue} />}
                />
              }
              href={
                "/sheets/info?question=bonds.details.ytm.question&answer=bonds.details.ytm.answer"
              }
            >
              {() => (
                <BondsTooltipPage
                  question={"bonds.details.ytm.question"}
                  answer={"bonds.details.ytm.answer"}
                />
              )}
            </AdaptiveModal>
          )}
          {tenure?.labelDataKey && tenure?.valueDataKey && (
            <AdaptiveModal
              size="small"
              href={
                "/sheets/info?question=bonds.details.tenure.question&answer=bonds.details.tenure.answer"
              }
              trigger={
                <HorizontalListItem
                  className="flex-shrink-0 basis-8 cursor-pointer"
                  label={
                    <span className="flex items-center gap-1">
                      {tenureLabel}
                    </span>
                  }
                  value={<RichText text={tenureValue} />}
                />
              }
            >
              {() => (
                <BondsTooltipPage
                  question={"bonds.details.tenure.question"}
                  answer={"bonds.details.tenure.answer"}
                />
              )}
            </AdaptiveModal>
          )}
          {soldUnits?.labelDataKey && soldUnits?.valueDataKey && (
            <AdaptiveModal
              size="small"
              href={
                "/sheets/info?question=bonds.details.soldUnits.question&answer=bonds.details.soldUnits.answer"
              }
              trigger={
                <HorizontalListItem
                  className="cursor-pointer"
                  label={
                    <span className="flex items-center gap-1">
                      {soldUnitsLabel}
                    </span>
                  }
                  value={
                    <>
                      <RichText text={soldUnitsValue} />
                      {availableUnitsPercent > 50 && (
                        <ProgressBar
                          progress={availableUnitsPercent}
                          barColor={getSoldProgress(availableUnitsPercent)}
                          bgColor="#D9D9D9"
                          className="mt-1"
                        />
                      )}
                    </>
                  }
                />
              }
            >
              {() => (
                <BondsTooltipPage
                  question={"bonds.details.soldUnits.question"}
                  answer={"bonds.details.soldUnits.answer"}
                />
              )}
            </AdaptiveModal>
          )}
        </HorizontalList>
      </Card>
    </div>
  );
}
