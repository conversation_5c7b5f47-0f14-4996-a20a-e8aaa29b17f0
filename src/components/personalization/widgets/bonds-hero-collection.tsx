import { useState, useRef, type HTMLAttributes } from "react";
import { useQuery } from "@tanstack/react-query";
import SectionHeading from "@/components/ui/section-heading/section-heading";
import QueryRenderer from "@/components/functional/query-renderer";
import { createCollectionQueryOptions } from "@/queries/bonds";

import { type BondsHeroCollectionProps } from "@/clients/gen/personalization_api";
import FeaturedBondCard from "@/components/bonds/featured-bond-card";
import CompactBondCard from "@/components/bonds/compact-bond-card";
import ViewMoreButton from "@/components/bonds/view-more-button";
import { useMediaQuery } from "@react-hook/media-query";

type BondsHeroCollectionComponentProps = BondsHeroCollectionProps &
  HTMLAttributes<HTMLDivElement>;

export default function BondsHeroCollection({
  collectionName,
  viewAllMinLimit,
  ...rest
}: BondsHeroCollectionComponentProps) {
  const query = useQuery(createCollectionQueryOptions(collectionName));
  const [activeIndex, setActiveIndex] = useState(0);
  const [showAll, setShowAll] = useState(false);
  const viewMoreButtonRef = useRef<HTMLDivElement>(null);
  const isDesktop = useMediaQuery("(min-width: 768px)");

  const toggleCards = () => {
    if (showAll) {
      viewMoreButtonRef.current?.scrollIntoView({
        behavior: "smooth",
        block: "end",
      });

      setTimeout(() => {
        setActiveIndex(0);
        setShowAll(!showAll);
      }, 200);
    } else {
      setShowAll(!showAll);
    }
  };

  const handleCardClick = (index: number) => {
    if (index === activeIndex) return;
    setActiveIndex(index);
  };

  const loader = (
    <div className="animate-pulse space-y-6 p-5">
      <div className="bg-black-5 h-5 w-48 rounded-lg"></div>
      <div>
        <div className="h-[393px] w-full"></div>
        <div className="h-[88px] w-full"></div>
        <div className="h-[88px] w-full"></div>
      </div>
    </div>
  );

  return (
    <div {...rest} data-widget="BondsCollection" className="max-sm:px-5">
      <QueryRenderer query={query} loader={loader}>
        {(collection) => (
          <div className="space-y-4 md:space-y-6" ref={viewMoreButtonRef}>
            <SectionHeading
              size={isDesktop ? "small" : "medium"}
              separator={isDesktop}
              title={collection.title}
            />
            <div className="space-y-4">
              {collection.collectionItem
                .slice(
                  0,
                  viewAllMinLimit
                    ? showAll
                      ? collection.collectionItem.length
                      : viewAllMinLimit
                    : collection.collectionItem.length
                )
                .map((item, index) => {
                  if (index === activeIndex) {
                    return (
                      <div
                        key={item.id}
                        className={`transform transition-all duration-300 ease-in-out`}
                      >
                        <FeaturedBondCard
                          item={item}
                          collectionName={collectionName}
                          rank={index + 1}
                          indexed
                        />
                      </div>
                    );
                  }
                  return (
                    <button
                      key={item.id}
                      className={`block w-full transform cursor-pointer transition-all duration-200 ease-in-out hover:scale-[1.02]`}
                      onClick={() => handleCardClick(index)}
                    >
                      <CompactBondCard
                        item={item}
                        rank={index + 1}
                        indexed
                        expandable
                      />
                    </button>
                  );
                })}
            </div>
            {viewAllMinLimit &&
              collection.collectionItem.length > viewAllMinLimit && (
                <ViewMoreButton
                  isExpanded={showAll}
                  handleClick={toggleCards}
                />
              )}
          </div>
        )}
      </QueryRenderer>
    </div>
  );
}
