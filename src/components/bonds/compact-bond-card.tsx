import type { CollectionItem } from "@/clients/gen/broking/Collection_pb";
import type { HTMLAttributes } from "react";
import chevron from "@/assets/images/icons/chevron.svg";
import EntityLogo from "../ui/entity-logo/entity-logo";
import Surface from "../ui/surface/surface";
import { formatAmount } from "@/utils/format";
import redStrike from "@/assets/images/illustrations/red-strike.webp";
import GradientNumber from "../ui/gradient-number";
import Anchor from "../functional/anchor";
import { makeBondUrl } from "@/utils/routing";
import { trackEvent } from "@/utils/analytics";
import { InvestabilityStatus } from "@/clients/gen/broking/BondDetails_pb";
import { useMediaQuery } from "@react-hook/media-query";
import Tag from "../ui/tag/tag";
import clsx from "clsx";

type CollectionItemProps = {
  item: CollectionItem;
  collectionName?: string;
  indexed?: boolean;
  expandable?: boolean;
  rank?: number;
  className?: string;
} & HTMLAttributes<HTMLAnchorElement>;

function CompactBondCardTag({ item }: CollectionItemProps) {
  return (
    <div className="absolute -top-3 left-3 sm:left-9">
      {item.tagConfig &&
        (item.tagConfig?.name && item.tagConfig?.type === "tag" ? (
          <Tag
            color={item.tagConfig.color}
            backgroundColor={item.tagConfig.bgColor}
            shimmerColor="#916CFF"
            borderColor="#916CFF33"
            hasShimmer
          >
            {item.tagConfig?.iconUrl && (
              <img
                decoding="sync"
                src={item.tagConfig?.iconUrl}
                className="mr-1 w-2"
                alt="Icon"
              />
            )}
            <span className="whitespace-nowrap"> {item.tagConfig.name} </span>
          </Tag>
        ) : (
          <div className="mt-1 flex items-center">
            {item.tagConfig?.iconUrl && (
              <img
                decoding="sync"
                src={item.tagConfig?.iconUrl}
                className="mr-1 w-2"
                alt="Icon"
              />
            )}
            <span className="text-body2 text-black-50 line-clamp-1 text-ellipsis">
              {item.tagConfig?.name}
            </span>
          </div>
        ))}
    </div>
  );
}

function CompactBondCardBody({
  item,
  rank = 0,
  indexed = false,
  expandable = false,
}: CollectionItemProps) {
  const isDesktop = useMediaQuery("(min-width: 768px)");
  return (
    <Surface elevation="md">
      <div className="relative flex items-center gap-2 p-3 md:gap-5 md:px-9 md:py-6">
        {item?.aboutTheInstitution?.logo && (
          <EntityLogo
            size={isDesktop ? "large" : "medium"}
            url={item?.aboutTheInstitution?.logo}
            color={"#FFFFFF"}
            elevation="none"
            greyBorder
          />
        )}
        <div className="md:space-y-1">
          <p className="relative space-x-[4px] bg-white text-left md:space-x-[6.8px]">
            <span className="text-black-80 text-heading3 font-medium">
              {formatAmount(item.xirr)}%
            </span>
            {item.struckenYield > 0 && (
              <span className="relative inline-block">
                <span className="text-black-40 text-body2 font-medium">
                  {formatAmount(item.struckenYield)}%
                </span>
                <img
                  src={redStrike}
                  alt="red-strike"
                  className="absolute top-2.5 left-0 z-20 w-6 md:w-11"
                />
              </span>
            )}
            <span className="text-black-50 text-body2">YTM</span>
          </p>
          {expandable ? (
            <div className="flex items-center gap-[6px] max-sm:w-46 md:gap-3">
              <p className="text-black-80 text-body1 flex-shrink-0">
                {item.tenure}
              </p>
              <div className="bg-black-40 h-1 w-1 flex-shrink-0 rounded-full md:h-2 md:w-2"></div>
              <p className="text-black-40 text-body1 truncate">
                {item.displayTitle}
              </p>
            </div>
          ) : (
            <div className="flex items-center gap-[6px] max-sm:w-40 md:gap-3">
              <p className="text-black-80 text-body1 flex-shrink-0">
                {item.tenure}
              </p>
              <div className="bg-black-40 h-1 w-1 flex-shrink-0 rounded-full md:h-2 md:w-2"></div>
              <p className="text-black-40 text-body1 truncate">
                {item.displayTitle}
              </p>

              <img
                src={chevron}
                alt="chevron-right"
                className="-mt-0.5 w-[6px] md:w-[7px] md:pt-[1px]"
              />
            </div>
          )}
        </div>
        {indexed && rank && (
          <GradientNumber
            value={rank}
            className="absolute right-1 -bottom-7 text-[64px] md:right-3 md:-bottom-11 md:text-[108px]"
          />
        )}
      </div>
    </Surface>
  );
}

export default function CompactBondCard({
  item,
  rank = 0,
  indexed = false,
  expandable = false,
  className,
  collectionName = "",
  ...rest
}: CollectionItemProps) {
  const handleBondsCollectionItemClicked = () => {
    trackEvent("bonds_collection_item_clicked", {
      bond_name: item.aboutTheInstitution?.bondInstitutionName,
      bond_type: InvestabilityStatus[item.investabilityStatus],
      bond_id: item.id,
      collectionName,
      rank,
    });
  };

  return expandable ? (
    <div
      className={clsx(
        "relative",
        item.investabilityStatus === InvestabilityStatus.SOLD_OUT &&
          "opacity-60"
      )}
    >
      <CompactBondCardBody
        item={item}
        rank={rank}
        indexed={indexed}
        expandable={expandable}
      />
      <CompactBondCardTag item={item} />
    </div>
  ) : (
    <div
      className={clsx(
        "relative",
        item.investabilityStatus === InvestabilityStatus.SOLD_OUT &&
          "opacity-60"
      )}
    >
      <Anchor
        href={makeBondUrl(item)}
        className={className}
        onClick={handleBondsCollectionItemClicked}
        {...rest}
      >
        <CompactBondCardBody
          item={item}
          rank={rank}
          indexed={indexed}
          expandable={expandable}
        />
        <CompactBondCardTag item={item} />
      </Anchor>
    </div>
  );
}
