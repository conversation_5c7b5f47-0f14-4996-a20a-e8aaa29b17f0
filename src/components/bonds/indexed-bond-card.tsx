import Surface from "@/components/ui/surface/surface";
import Tag from "@/components/ui/tag/tag";
import EntityLogo from "@/components/ui/entity-logo/entity-logo";
import Anchor from "@/components/functional/anchor";
import { formatAmount } from "@/utils/format";
import { makeBondUrl } from "@/utils/routing";
import type { CollectionItem } from "@/clients/gen/broking/Collection_pb";

type IndexedCollectionItemProps = {
  item: CollectionItem;
  rank: number;
};

export default function IndexedBondCard({
  item,
  rank,
}: IndexedCollectionItemProps) {
  return (
    <Anchor href={makeBondUrl(item)} className="relative flex h-full">
      <span
        className="absolute -bottom-4 text-[150px] leading-none font-medium text-transparent"
        style={{ WebkitTextStroke: "var(--border-w-sm) var(--color-purple)" }}
      >
        {rank + 1}
      </span>
      <div className="ml-10">
        <Surface elevation="md" className="relative">
          <div className="w-39 rounded-xl bg-gradient-to-tr from-white via-white to-[#F2EDFF80] p-4">
            <div className="flex justify-between">
              <div className="flex items-center gap-3">
                <div className="bg-purple/50 flex h-10 w-1 rounded-sm"></div>
                <div className="flex-col">
                  <h4 className="text-heading2 font-medium">
                    {formatAmount(item.xirr)}%
                  </h4>
                  <p className="text-black-40 text-body2">for {item.tenure}</p>
                </div>
              </div>
              <div>
                <svg
                  width="10"
                  height="10"
                  viewBox="0 0 10 10"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="inline-block size-3 align-middle"
                >
                  <path
                    d="M9.1 7.58009L9.1 7.68009L9 7.68009L8.04968 7.68009L7.94933 7.68009L7.94968 7.57974L7.96611 2.85303L1.76193 9.07063L1.69203 9.14068L1.62126 9.07152L0.930113 8.3962L0.857932 8.32567L0.929117 8.25414L7.11794 2.03507L2.4514 2.03507L2.3514 2.03507L2.3514 1.93507L2.3514 1L2.3514 0.900001L2.4514 0.900001L9 0.900001L9.1 0.900001L9.1 1L9.1 7.58009Z"
                    fill="currentColor"
                    stroke="currentColor"
                    strokeWidth="0.2"
                  />
                </svg>
              </div>
            </div>
            <div className="min-h-[42px] pt-4">
              {item.tagConfig?.name &&
                (item.tagConfig?.type === "tag" ? (
                  <Tag
                    color={item.tagConfig.color}
                    backgroundColor={item.tagConfig.bgColor}
                    shimmerColor="purple/50"
                    borderColor="#916CFF33"
                    hasShimmer={true}
                  >
                    {item.tagConfig?.iconUrl && (
                      <img
                        decoding="sync"
                        src={item.tagConfig.iconUrl}
                        className="mr-1 w-2"
                        alt=""
                      />
                    )}
                    <span className="tracking-wider">
                      {" "}
                      {item.tagConfig.name}{" "}
                    </span>
                  </Tag>
                ) : (
                  <div className="mt-1 flex items-center">
                    {item.tagConfig?.iconUrl && (
                      <img
                        decoding="sync"
                        src={item.tagConfig?.iconUrl}
                        className="mr-1 w-2"
                        alt=""
                      />
                    )}
                    <span className="text-body2 text-black-50 line-clamp-1 text-ellipsis">
                      {item.tagConfig.name}
                    </span>
                  </div>
                ))}
            </div>
            <div className="flex min-h-25 items-center gap-3 pt-14">
              <EntityLogo
                size="medium"
                url={item.aboutTheInstitution?.logo || ""}
                color={item.bgColor}
                seamless
                elevation="none"
              />
              <h4 className="text-heading4 line-clamp-2 flex-1 text-wrap">
                {item.displayTitle}
              </h4>
            </div>
          </div>
        </Surface>
      </div>
    </Anchor>
  );
}
