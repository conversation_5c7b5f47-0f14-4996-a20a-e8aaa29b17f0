import type { BondItem } from "@/clients/gen/broking/FilterSearch_pb";
import Tag from "@/components/ui/tag/tag";
import { formatAmount } from "@/utils/format";
import Anchor from "../functional/anchor";
import { trackEvent } from "@/utils/analytics";
import { EventProps, FilterSearchEvents } from "@/pages/sort-filter/events";
import { isFlutterWebView, navigate } from "@/utils/routing";
import { useQuery } from "@tanstack/react-query";
import { isEnabled } from "@/utils/feature-flags";

const HorizontalBondsCard = ({ item }: { item: BondItem }) => {
  const featureFlageQuery = useQuery({
    queryKey: ["feature-flags"],
    queryFn: () => isEnabled("filter-redirection-path-ab"),
  });

  const isCalculatorRedirectionEnabled = featureFlageQuery.data;

  const handleAnchorClick = (
    event: React.MouseEvent,
    eventName: string,
    url: string
  ) => {
    trackEvent(eventName, {
      [EventProps.bondName]: item.displayTitle,
      [EventProps.isin]: item.isin,
      [EventProps.investabilityStatus]: item.investabilityStatus,
      [EventProps.bondXirr]: item.ytm,
      [EventProps.rating]: item.rating,
    });
    // @ts-expect-error check error
    navigate(`${window.origin}${url}`, event);
  };

  const bondUrl = isCalculatorRedirectionEnabled
    ? `/bonds/${item.slug ?? "unknown"}/${item.isin}/calculator`
    : `/bonds/${item.slug ?? "unknown"}/${item.isin}`;

  return (
    <div className="flex flex-col gap-4 px-5">
      <div className="flex items-center">
        <div className="flex items-center">
          <Anchor
            href={
              isFlutterWebView()
                ? undefined
                : `/bonds/${item.slug ?? "unknown"}/${item.isin}`
            }
            onClick={(event) =>
              handleAnchorClick(
                event,
                FilterSearchEvents.bondsSearchFilterResultIconClicked,
                `/bonds/${item.slug ?? "unknown"}/${item.isin}`
              )
            }
            className="block shrink-0"
          >
            <div className="border-black-10 flex h-11 w-11 items-center justify-center rounded-sm border-1">
              <img
                decoding="sync"
                alt="institution logo"
                className="h-6 w-6 object-contain"
                src={item.logoUrl}
              />
            </div>
          </Anchor>
        </div>
        <Anchor
          href={isFlutterWebView() ? undefined : bondUrl}
          onClick={(event) =>
            handleAnchorClick(
              event,
              FilterSearchEvents.bondsSearchFilterResultItemClicked,
              bondUrl
            )
          }
          className="flex-1"
        >
          <div className="flex flex-grow-1 justify-between gap-3 pl-4">
            <div className="flex flex-col gap-0.5">
              <div className="flex gap-1.5">
                <p
                  className={`text-heading4 ${item.tag?.name ? "max-w-[10ch] truncate" : ""}`}
                >
                  {item.tenure}
                </p>
                {item.tag?.name && (
                  <Tag
                    color={item.tag?.color}
                    backgroundColor={item.tag?.bgColor}
                    borderColor=""
                    hasShimmer
                    shimmerColor={`${item.tag.color}66`}
                  >
                    {item.tag?.name}
                  </Tag>
                )}
              </div>

              <div className="flex items-center gap-1">
                <p className="text-body2 text-black-50 max-w-[22ch] truncate">
                  {item.displayTitle}
                </p>
              </div>
            </div>

            <div className="flex flex-1 items-center justify-end gap-1">
              <div className="flex flex-col">
                <p className="text-green text-heading4 text-right font-medium">
                  {formatAmount(item.ytm)}%
                </p>
                <p className="text-body2 text-black-40 text-right">
                  {item.rating}
                </p>
              </div>

              <svg
                width="9"
                height="5"
                viewBox="0 0 9 5"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className={
                  "block flex-shrink-0 -rotate-90 opacity-60 transition-transform duration-300"
                }
              >
                <path
                  d="M8 0.75L4.5 4.25L1 0.75"
                  stroke={"black"}
                  strokeOpacity="0.8"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </div>
          </div>
        </Anchor>
      </div>
    </div>
  );
};

export default HorizontalBondsCard;
