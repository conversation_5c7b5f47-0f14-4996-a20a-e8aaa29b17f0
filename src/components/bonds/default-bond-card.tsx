import { type HTMLAttributes } from "react";
import clsx from "clsx";
import Surface from "@/components/ui/surface/surface";
import EntityLogo from "@/components/ui/entity-logo/entity-logo";
import Tag from "@/components/ui/tag/tag";
import Button from "@/components/ui/button/button";
import Anchor from "@/components/functional/anchor";
import { makeBondUrl } from "@/utils/routing";
import { trackEvent } from "@/utils/analytics";
import { InvestabilityStatus } from "@/clients/gen/broking/BondDetails_pb";
import type { CollectionItem } from "@/clients/gen/broking/Collection_pb";
import i18n from "i18next";
import styles from "./collection-item.module.css";
import { formatAmount } from "@/utils/format";

type CollectionItemProps = {
  item: CollectionItem;
  collectionName: string;
  rank: number;
  className?: string;
} & HTMLAttributes<HTMLAnchorElement>;

function getBondsInvestibilityStatus(
  investabilityStatus: InvestabilityStatus
): string {
  switch (investabilityStatus) {
    case InvestabilityStatus.LIVE:
      return i18n.t("bonds.collections.button.active", "Invest");
    case InvestabilityStatus.INVESTABILITY_STATUS_UNKNOWN:
      return i18n.t("bonds.collections.button.unknown", "Unknown");
    case InvestabilityStatus.COMING_SOON:
      return i18n.t("bonds.collections.button.comingSoon", "Coming Soon");
    case InvestabilityStatus.SOLD_OUT:
      return i18n.t("bonds.collections.button.soldOut", "Sold Out");
    default:
      return i18n.t("bonds.collections.button.unknown", "Unknown");
  }
}

export default function DefaultBondCard({
  item,
  collectionName = "",
  rank = 0,
  className,
  ...rest
}: CollectionItemProps) {
  const handleBondsCollectionItemClicked = () => {
    trackEvent("bonds_collection_item_clicked", {
      bond_name: item.aboutTheInstitution?.bondInstitutionName,
      bond_type: InvestabilityStatus[item.investabilityStatus],
      bond_id: item.id,
      collectionName,
      rank,
    });
  };
  return (
    <Anchor
      href={makeBondUrl(item)}
      className={clsx("inline-block shrink-0", className)}
      onClick={handleBondsCollectionItemClicked}
      {...rest}
    >
      <Surface>
        <img
          decoding="sync"
          src={item.coverImageUrl}
          alt={item.aboutTheInstitution?.title}
        />
        <div
          className={clsx(
            "relative -mt-2 rounded-tr-sm bg-white bg-right-bottom bg-no-repeat p-4",
            styles.ripple
          )}
        >
          <div className="-mt-9 mb-2">
            <EntityLogo
              size="medium"
              url={item.aboutTheInstitution!.logo}
              color={item.bgColor}
              seamless
              elevation="md"
            />
          </div>
          <div className="h-[3em] md:h-[4em]">
            <h4 className="md:text-heading3 text-heading4 line-clamp-2 text-wrap text-ellipsis">
              {item.displayTitle}
            </h4>
            <div className="pt-1">
              {item.tagConfig?.name && item.tagConfig?.type === "tag" ? (
                <Tag
                  color={item.tagConfig.color}
                  backgroundColor={item.tagConfig.bgColor}
                  shimmerColor="#916CFF"
                  borderColor="#916CFF33"
                  hasShimmer
                >
                  {item.tagConfig?.iconUrl && (
                    <img
                      decoding="sync"
                      src={item.tagConfig?.iconUrl}
                      className="mr-1 w-2"
                      alt="Icon"
                    />
                  )}
                  <span> {item.tagConfig.name} </span>
                </Tag>
              ) : (
                <div className="mt-1 flex items-center">
                  {item.tagConfig?.iconUrl && (
                    <img
                      decoding="sync"
                      src={item.tagConfig?.iconUrl}
                      className="mr-1 w-2"
                      alt="Icon"
                    />
                  )}
                  <span className="text-body2 text-black-50 line-clamp-1 text-ellipsis">
                    {item.tagConfig?.name}
                  </span>
                </div>
              )}
            </div>
          </div>
          <div className="flex md:hidden">
            <p className="pt-6 pb-4">
              <span className="text-heading1 pr-1 font-medium">
                {formatAmount(item.xirr)}%
              </span>
              <span className="text-body2 text-black-50">YTM</span>
            </p>
          </div>
          <div className="hidden md:flex">
            <p className="pt-6 pb-4">
              <span className="pr-1 text-[33px] font-medium">
                {formatAmount(item.xirr)}%
              </span>
              <span className="text-body1 text-black-50">YTM</span>
            </p>
          </div>

          <div className="flex md:hidden">
            <Button
              size="small"
              disabled={
                item.investabilityStatus === InvestabilityStatus.SOLD_OUT
              }
            >
              {getBondsInvestibilityStatus(item.investabilityStatus)}
            </Button>
          </div>
          <div className="hidden w-32 md:flex">
            <Button
              size="medium"
              disabled={
                item.investabilityStatus === InvestabilityStatus.SOLD_OUT
              }
            >
              {getBondsInvestibilityStatus(item.investabilityStatus)}
            </Button>
          </div>
        </div>
      </Surface>
    </Anchor>
  );
}
