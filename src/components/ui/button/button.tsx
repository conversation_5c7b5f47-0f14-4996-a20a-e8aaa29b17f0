import clsx from "clsx";
import type { ButtonProps } from "./types";
import { useSize } from "@/hooks/size";
import { useRef } from "react";
import Anchor from "@/components/functional/anchor";
import CircularProgressIndicator from "@/components/icons/circular-progress-indicator";

export default function Button({
  className,
  size = "medium",
  href,
  children,
  disabled,
  hasShimmer,
  loading,
  replace,
  variant = "primary",
  ...props
}: ButtonProps) {
  const buttonRef = useRef<HTMLButtonElement | HTMLAnchorElement | null>(null);
  const buttonSize = useSize(buttonRef);
  const classes = clsx(
    "relative flex items-center justify-center gap-2 font-medium cursor-pointer flex-shrink-0",
    variant === "primary"
      ? disabled
        ? " text-white bg-black-20"
        : " text-white bg-black-80"
      : "bg-white text-black-80 border",
    {
      "px-5 text-heading4 w-full h-12 rounded-xl": size === "medium",
      "px-3 text-body1 h-8 rounded-lg": size === "small",
    },
    className
  );
  const content = (
    <>
      {hasShimmer ? (
        <svg
          width="46"
          height="48"
          viewBox="0 0 46 48"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className="absolute top-0 left-0 z-10 size-12"
          style={{ animation: "left-to-right 2.5s ease infinite" }}
        >
          <path
            opacity="0.13"
            d="M22.6667 0H46L34.3333 48H11L22.6667 0Z"
            fill="currentColor"
          />
          <path
            opacity="0.13"
            d="M11.4 0H19L7.6 48H0L11.4 0Z"
            fill="currentColor"
          />
        </svg>
      ) : null}
      <span>{children}</span>
      {loading ? <CircularProgressIndicator /> : null}
    </>
  );
  if (href) {
    return (
      <Anchor
        className={classes}
        href={href}
        replace={replace}
        data-loading={loading}
        {...props}
        ref={buttonRef as React.Ref<HTMLAnchorElement>}
        style={
          {
            "--translate-width": `${buttonSize?.width ?? 0}px`,
          } as React.CSSProperties
        }
      >
        {content}
      </Anchor>
    );
  }
  return (
    <button
      className={classes}
      disabled={disabled || loading}
      data-loading={loading}
      {...props}
      ref={buttonRef as React.Ref<HTMLButtonElement>}
      style={
        {
          "--translate-width": `${buttonSize?.width ?? 0}px`,
        } as React.CSSProperties
      }
    >
      {content}
    </button>
  );
}
