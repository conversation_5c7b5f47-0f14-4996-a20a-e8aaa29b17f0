import type { ReactNode, DetailedHTMLProps, InputHTMLAttributes } from "react";
import type { FieldMetaProps } from "formik";

export type InputProps = {
  error?: string;
  mask?: "inr" | "date" | "pan" | "number" | "mobile_number" | "otp";
  label?: string;
  textTransform?: "uppercase" | "lowercase" | "capitalize" | "none";
  density?: "default" | "compact";
  prefix?: ReactNode;
  suffix?: ReactNode;
  help?: ReactNode;
  meta?: FieldMetaProps<string>;
} & Omit<
  DetailedHTMLProps<InputHTMLAttributes<HTMLInputElement>, HTMLInputElement>,
  "prefix"
>;
