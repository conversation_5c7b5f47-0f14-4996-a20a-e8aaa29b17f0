import React, {
  useState,
  useRef,
  useEffect,
  useCallback,
  useMemo,
} from "react";
import "./index.css";
import ReactDOM from "react-dom";
import clsx from "clsx";
import Checkbox from "@/components/ui/checkbox/checkbox";
import type { AppliedFilter } from "@/pages/sort-filter/machine";
import type { PositioningOptions } from "@zag-js/popper";
import { trackEvent } from "@/utils/analytics";

interface Option {
  label: string;
  value: string | number;
}

interface CheckBoxDropdownProps {
  options: Option[];
  placeholder?: string;
  onChange: (index: number, isSelected: boolean) => void;
  value?: string | number;
  appliedFilters: AppliedFilter[];
  positioning?: Partial<PositioningOptions>;
  eventName?: string;
}

const CheckBoxDropdown: React.FC<CheckBoxDropdownProps> = ({
  options,
  placeholder = "Select",
  onChange,
  appliedFilters,
  positioning = {},
  eventName,
}) => {
  const [open, setOpen] = useState(false);
  const [isClosing, setIsClosing] = useState(false);
  const ref = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLUListElement>(null);
  const [dropdownStyle, setDropdownStyle] = useState<React.CSSProperties>({});

  // Extract positioning options to prevent object recreation issues
  const placement = positioning.placement ?? "bottom";
  const strategy = positioning.strategy ?? "absolute";
  const flip = positioning.flip ?? true;
  const sameWidth = positioning.sameWidth ?? false;
  const fitViewport = positioning.fitViewport ?? true;
  const gutter = positioning.gutter ?? 8;

  // Memoize offset values to prevent unnecessary re-renders
  const offsetValues = useMemo(
    () => ({
      mainAxis: positioning.offset?.mainAxis ?? gutter,
      crossAxis: positioning.offset?.crossAxis ?? 0,
    }),
    [positioning.offset?.mainAxis, positioning.offset?.crossAxis, gutter]
  );

  const closeDropdown = () => {
    trackEvent("multiselect_checkbox_closed");
    setIsClosing(true);
    setTimeout(() => {
      setOpen(false);
      setIsClosing(false);
    }, 150);
  };

  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      const target = e.target as Node;

      const clickedOutside =
        ref.current &&
        !ref.current.contains(target) &&
        dropdownRef.current &&
        !dropdownRef.current.contains(target);

      if (clickedOutside && open && !isClosing) {
        closeDropdown();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [open, isClosing]);

  const calculateDropdownPosition = useCallback(() => {
    if (!ref.current) return {};

    const rect = ref.current.getBoundingClientRect();
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight,
    };

    let top = rect.bottom + window.scrollY;
    let left = rect.left + window.scrollX;
    const width = sameWidth ? rect.width : "fit-content";
    const minWidth = sameWidth ? rect.width : 154;
    const maxWidth = sameWidth ? rect.width : 200;

    const mainAxisOffset = offsetValues.mainAxis;
    const crossAxisOffset = offsetValues.crossAxis;

    switch (placement) {
      case "top":
        top = rect.top + window.scrollY - mainAxisOffset;
        left = rect.left + window.scrollX + crossAxisOffset;
        break;
      case "bottom":
        top = rect.bottom + window.scrollY + mainAxisOffset;
        left = rect.left + window.scrollX + crossAxisOffset;
        break;
      case "left":
        top = rect.top + window.scrollY + crossAxisOffset;
        left = rect.left + window.scrollX - mainAxisOffset;
        break;
      case "right":
        top = rect.top + window.scrollY + crossAxisOffset;
        left = rect.right + window.scrollX + mainAxisOffset;
        break;
      case "top-start":
        top = rect.top + window.scrollY - mainAxisOffset;
        left = rect.left + window.scrollX + crossAxisOffset;
        break;
      case "top-end":
        top = rect.top + window.scrollY - mainAxisOffset;
        left = rect.right + window.scrollX - crossAxisOffset;
        break;
      case "bottom-start":
        top = rect.bottom + window.scrollY + mainAxisOffset;
        left = rect.left + window.scrollX + crossAxisOffset;
        break;
      case "bottom-end":
        top = rect.bottom + window.scrollY + mainAxisOffset;
        left = rect.right + window.scrollX - crossAxisOffset;
        break;
      case "left-start":
        top = rect.top + window.scrollY + crossAxisOffset;
        left = rect.left + window.scrollX - mainAxisOffset;
        break;
      case "left-end":
        top = rect.bottom + window.scrollY - crossAxisOffset;
        left = rect.left + window.scrollX - mainAxisOffset;
        break;
      case "right-start":
        top = rect.top + window.scrollY + crossAxisOffset;
        left = rect.right + window.scrollX + mainAxisOffset;
        break;
      case "right-end":
        top = rect.bottom + window.scrollY - crossAxisOffset;
        left = rect.right + window.scrollX + mainAxisOffset;
        break;
      default:
        // Default to bottom
        top = rect.bottom + window.scrollY + mainAxisOffset;
        left = rect.left + window.scrollX + crossAxisOffset;
    }

    // Handle flipping if enabled and dropdown would go outside viewport
    if (flip && fitViewport) {
      const dropdownHeight = 200; // Approximate dropdown height
      const dropdownWidth = typeof minWidth === "number" ? minWidth : 154;

      // Flip vertically if needed
      if (
        placement.includes("bottom") &&
        top + dropdownHeight > viewport.height + window.scrollY
      ) {
        top = rect.top + window.scrollY - dropdownHeight - mainAxisOffset;
      } else if (
        placement.includes("top") &&
        top - dropdownHeight < window.scrollY
      ) {
        top = rect.bottom + window.scrollY + mainAxisOffset;
      }

      // Flip horizontally if needed
      if (
        placement.includes("right") &&
        left + dropdownWidth > viewport.width + window.scrollX
      ) {
        left = rect.left + window.scrollX - dropdownWidth - mainAxisOffset;
      } else if (
        placement.includes("left") &&
        left - dropdownWidth < window.scrollX
      ) {
        left = rect.right + window.scrollX + mainAxisOffset;
      }

      // Adjust horizontal position to stay within viewport
      if (left + dropdownWidth > viewport.width + window.scrollX) {
        left = viewport.width + window.scrollX - dropdownWidth - mainAxisOffset;
      }
      if (left < window.scrollX) {
        left = window.scrollX + mainAxisOffset;
      }
    }

    return {
      position: strategy,
      top,
      left,
      width,
      minWidth,
      maxWidth,
      zIndex: 1000,
    };
  }, [placement, strategy, offsetValues, flip, sameWidth, fitViewport]);

  useEffect(() => {
    if (open && ref.current) {
      setDropdownStyle(calculateDropdownPosition());
    }
  }, [open, calculateDropdownPosition]);

  // Recalculate position on window resize and scroll
  useEffect(() => {
    if (!open) return;

    const updatePosition = () => {
      if (ref.current) {
        setDropdownStyle(calculateDropdownPosition());
      }
    };

    window.addEventListener("resize", updatePosition);
    window.addEventListener("scroll", updatePosition, true);

    return () => {
      window.removeEventListener("resize", updatePosition);
      window.removeEventListener("scroll", updatePosition, true);
    };
  }, [open, calculateDropdownPosition]);

  const handleSelect = (index: number, isSelected: boolean) => {
    onChange(index, isSelected);
  };

  const checkSelected = (value: string) => {
    return appliedFilters.some((group) =>
      group.filters.some((filter) => filter.label === value)
    );
  };

  return (
    <div className="custom-select" ref={ref}>
      <div
        className={clsx(
          "select-box border-black-10 gap-1 border-1",
          open ? "open" : ""
        )}
        onClick={() => {
          if (open && !isClosing) {
            closeDropdown();
          } else if (!open && !isClosing) {
            setOpen(true);
            trackEvent(eventName ?? "multiselect_checkbox_opened");
          }
        }}
      >
        <span
          className={clsx(
            "text-body1",
            open ? "text-white" : "text-black-80 opacity-70"
          )}
        >
          {placeholder}
        </span>
        <span className="arrow w-[7px]">
          <svg
            width="9"
            height="5"
            viewBox="0 0 9 5"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className={clsx(
              "block flex-shrink-0 transition-transform duration-300",
              open ? "rotate-180" : "opacity-80"
            )}
          >
            <path
              d="M8 0.75L4.5 4.25L1 0.75"
              stroke={open ? "white" : "black"}
              strokeOpacity="0.8"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </span>
      </div>
      {open &&
        ReactDOM.createPortal(
          <ul
            className={clsx(
              "select-dropdown border-black-10 space-y-[10px] border-[0.3px]",
              {
                closing: isClosing,
              }
            )}
            style={dropdownStyle}
            ref={dropdownRef}
          >
            {options.map((opt, index) => (
              <div className="flex flex-col gap-[10px]" key={index}>
                <Checkbox
                  key={opt.value}
                  onChange={(e) => {
                    e.stopPropagation();
                    handleSelect(index, (e.target as HTMLInputElement).checked);
                  }}
                  checked={checkSelected(opt.label)}
                  className="text-black-80"
                >
                  {opt.label}
                </Checkbox>
                {index < options.length - 1 && (
                  <hr className="text-black-5 border-t-1" />
                )}
              </div>
            ))}
          </ul>,
          document.body
        )}
    </div>
  );
};

export default CheckBoxDropdown;
