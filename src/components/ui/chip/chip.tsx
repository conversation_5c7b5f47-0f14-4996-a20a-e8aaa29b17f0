import clsx from "clsx";
import type { ChipProps } from "./types";

export default function Chip({
  selected = false,
  children,
  caption,
  variant = "default",
  size = "medium",
  className,
  onClick,
  ...rest
}: ChipProps) {
  const contentClasses = clsx("flex items-center justify-center gap-2", {
    "text-heading4 py-2.5 px-4": size === "large",
    "text-body1 py-1.5 px-2.5": size === "medium",
  });

  const borderColor =
    selected && variant === "outlined" ? "#000000cc" : "#0000001A";

  return (
    <label
      className={clsx(
        "inline-block cursor-pointer overflow-hidden rounded-sm border-1 align-top",
        variant === "default"
          ? selected
            ? "bg-black-80 text-white"
            : "text-black-60"
          : selected
            ? "bg-black-3 text-black-80 border-black"
            : "border-black-10 text-black",
        className
      )}
      style={{ borderColor }}
      onClick={() => {
        onClick?.(!selected);
      }}
      {...rest}
    >
      <div className={contentClasses}>{children}</div>
      {caption ? (
        <div
          className="text-caption text-green flex items-center justify-center rounded-b-sm px-2 py-0.5 font-bold"
          style={{ backgroundColor: "#12BE571A" }}
        >
          {caption}
        </div>
      ) : null}
    </label>
  );
}
