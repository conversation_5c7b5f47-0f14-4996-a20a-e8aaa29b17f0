import { isHotwireNative } from "@/utils/routing";
import { createPortal } from "react-dom";
import { useLocation } from "react-router";

export default function UrlBar() {
  const location = useLocation();
  const displayUrl = window.location.host + location.pathname + location.search;
  if (!isHotwireNative() && !window.flutter_inappwebview) return null;
  return createPortal(
    <h5 className="bg-bg text-body2 text-black-50 isolate border-t p-2.5 text-center">
      <span className="line-clamp-1">{displayUrl}</span>
    </h5>,
    document.getElementById("url-bar")!
  );
}
