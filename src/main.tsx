// import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import "./app.css";
import "@hotwired/hotwire-native-bridge";
import "hotwire-native-bolt";
import App from "./app";
import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import Backend from "i18next-http-backend";
import dayjs from "dayjs";
import { initSentry } from "@/utils/sentry";
import customParseFormat from "dayjs/plugin/customParseFormat";
import { initAnalytics } from "./utils/analytics";

initSentry();
initAnalytics(import.meta.env.VITE_MIXPANEL_TOKEN);
dayjs.extend(customParseFormat);

i18n
  .use(Backend)
  .use(initReactI18next)
  .init({
    lng: "en",
    interpolation: {
      escapeValue: false,
      prefix: "{",
      suffix: "}",
    },
    backend: {
      loadPath: "https://assets.stablebonds.in/translations/en.json",
    },
  });

setTimeout(() => {
  window.prerenderReady = true;
}, 1000);

const floatingFooter = document.getElementById("floating-footer")!;
const resizeObserver = new ResizeObserver((entries) => {
  for (const entry of entries) {
    const height = Math.ceil(entry.contentRect.height);
    document.body.style.setProperty("--floating-footer-height", `${height}px`);
  }
});
resizeObserver.observe(floatingFooter);
function setScalingFactor() {
  const clampedFactor =
    document.body.clientWidth > 768
      ? 1
      : Math.min(Math.max(document.body.clientWidth / 360, 1), 1.2);
  document.documentElement.style.setProperty(
    "--sd-scale-factor",
    clampedFactor.toFixed(2)
  );
}
window.addEventListener("resize", setScalingFactor);
setScalingFactor();

createRoot(document.getElementById("root")!).render(
  // <StrictMode>
  <App />
  // </StrictMode>
);
