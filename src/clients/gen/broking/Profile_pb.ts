// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file Profile.proto (package com.stablemoney.api.broking, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { OnboardingModule } from "./Onboarding_pb.js";
import { file_Onboarding } from "./Onboarding_pb.js";
import type { AddressProto, EmploymentType, Gender, IncomeRange, MaritalStatus, TradingExperience } from "./Common_pb.js";
import { file_Common } from "./Common_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file Profile.proto.
 */
export const file_Profile: GenFile = /*@__PURE__*/
  fileDesc("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", [file_Onboarding, file_Common]);

/**
 * @generated from message com.stablemoney.api.broking.UserData
 */
export type UserData = Message<"com.stablemoney.api.broking.UserData"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string email = 2;
   */
  email: string;

  /**
   * @generated from field: bool email_verified = 3;
   */
  emailVerified: boolean;

  /**
   * @generated from field: string mobile = 4;
   */
  mobile: string;

  /**
   * @generated from field: bool mobile_verified = 5;
   */
  mobileVerified: boolean;

  /**
   * @generated from field: string name = 6;
   */
  name: string;

  /**
   * @generated from field: com.stablemoney.api.broking.UserStatus status = 7;
   */
  status: UserStatus;

  /**
   * @generated from field: string masked_email = 8;
   */
  maskedEmail: string;

  /**
   * @generated from field: string first_name = 9;
   */
  firstName: string;

  /**
   * @generated from field: string last_name = 10;
   */
  lastName: string;

  /**
   * @generated from field: string social_name = 11;
   */
  socialName: string;

  /**
   * @generated from field: string profile_image_url = 12;
   */
  profileImageUrl: string;
};

/**
 * Describes the message com.stablemoney.api.broking.UserData.
 * Use `create(UserDataSchema)` to create a new message.
 */
export const UserDataSchema: GenMessage<UserData> = /*@__PURE__*/
  messageDesc(file_Profile, 0);

/**
 * @generated from message com.stablemoney.api.broking.NomineeDetail
 */
export type NomineeDetail = Message<"com.stablemoney.api.broking.NomineeDetail"> & {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: string dob = 2;
   */
  dob: string;

  /**
   * @generated from field: string relationship = 3;
   */
  relationship: string;
};

/**
 * Describes the message com.stablemoney.api.broking.NomineeDetail.
 * Use `create(NomineeDetailSchema)` to create a new message.
 */
export const NomineeDetailSchema: GenMessage<NomineeDetail> = /*@__PURE__*/
  messageDesc(file_Profile, 1);

/**
 * @generated from message com.stablemoney.api.broking.UserProfileResponse
 */
export type UserProfileResponse = Message<"com.stablemoney.api.broking.UserProfileResponse"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.UserData data = 2;
   */
  data?: UserData;

  /**
   * @generated from field: com.stablemoney.api.broking.UserProfileData profile_data = 3;
   */
  profileData?: UserProfileData;

  /**
   * @generated from field: repeated com.stablemoney.api.broking.OnboardingModuleStatusData module_status = 4;
   */
  moduleStatus: OnboardingModuleStatusData[];

  /**
   * @generated from field: string life_time_status = 5;
   */
  lifeTimeStatus: string;

  /**
   * KYC_NOT_INITIATED , KYC_INITIATED , KYC_COMPLETED, BOND_PURCHASED
   *
   * @generated from field: string current_status = 6;
   */
  currentStatus: string;

  /**
   * @generated from field: optional string account_opening_form_url = 7;
   */
  accountOpeningFormUrl?: string;

  /**
   * @generated from field: optional com.stablemoney.api.broking.BankAccountDetail bank_account_detail = 8;
   */
  bankAccountDetail?: BankAccountDetail;

  /**
   * @generated from field: optional com.stablemoney.api.broking.DematAccountDetail demat_account_detail = 9;
   */
  dematAccountDetail?: DematAccountDetail;

  /**
   * @generated from field: bool cvl_validated = 10;
   */
  cvlValidated: boolean;

  /**
   * @generated from field: optional com.stablemoney.api.broking.NomineeDetail nominee_detail = 11;
   */
  nomineeDetail?: NomineeDetail;
};

/**
 * Describes the message com.stablemoney.api.broking.UserProfileResponse.
 * Use `create(UserProfileResponseSchema)` to create a new message.
 */
export const UserProfileResponseSchema: GenMessage<UserProfileResponse> = /*@__PURE__*/
  messageDesc(file_Profile, 2);

/**
 * @generated from message com.stablemoney.api.broking.UserProfileData
 */
export type UserProfileData = Message<"com.stablemoney.api.broking.UserProfileData"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string pan_number = 2;
   */
  panNumber: string;

  /**
   * @generated from field: string aadhar_number = 3;
   */
  aadharNumber: string;

  /**
   * @generated from field: string dob = 4;
   */
  dob: string;

  /**
   * @generated from field: com.stablemoney.api.broking.Gender gender = 5;
   */
  gender: Gender;

  /**
   * @generated from field: com.stablemoney.api.broking.IncomeRange income_range = 6;
   */
  incomeRange: IncomeRange;

  /**
   * @generated from field: com.stablemoney.api.broking.EmploymentType employment_type = 7;
   */
  employmentType: EmploymentType;

  /**
   * @generated from field: com.stablemoney.api.broking.TradingExperience trading_experience = 8;
   */
  tradingExperience: TradingExperience;

  /**
   * @generated from field: com.stablemoney.api.broking.MaritalStatus marital_status = 9;
   */
  maritalStatus: MaritalStatus;

  /**
   * @generated from field: string father_name = 10;
   */
  fatherName: string;

  /**
   * @generated from field: string mother_name = 11;
   */
  motherName: string;

  /**
   * @generated from field: string e_sign_url = 12;
   */
  eSignUrl: string;

  /**
   * @generated from field: string income_tax_department_name = 13;
   */
  incomeTaxDepartmentName: string;

  /**
   * @generated from field: string kra_name = 14;
   */
  kraName: string;

  /**
   * @generated from field: string referral_link = 15;
   */
  referralLink: string;

  /**
   * @generated from field: int32 fd_booking_count = 16;
   */
  fdBookingCount: number;

  /**
   * @generated from field: bool first_fd_reward_claimed = 17;
   */
  firstFdRewardClaimed: boolean;
};

/**
 * Describes the message com.stablemoney.api.broking.UserProfileData.
 * Use `create(UserProfileDataSchema)` to create a new message.
 */
export const UserProfileDataSchema: GenMessage<UserProfileData> = /*@__PURE__*/
  messageDesc(file_Profile, 3);

/**
 * @generated from message com.stablemoney.api.broking.BankAccountDetail
 */
export type BankAccountDetail = Message<"com.stablemoney.api.broking.BankAccountDetail"> & {
  /**
   * @generated from field: string bank_name = 1;
   */
  bankName: string;

  /**
   * @generated from field: string account_number_masked = 2;
   */
  accountNumberMasked: string;

  /**
   * @generated from field: string branch = 3;
   */
  branch: string;

  /**
   * @generated from field: string ifsc_code = 4;
   */
  ifscCode: string;
};

/**
 * Describes the message com.stablemoney.api.broking.BankAccountDetail.
 * Use `create(BankAccountDetailSchema)` to create a new message.
 */
export const BankAccountDetailSchema: GenMessage<BankAccountDetail> = /*@__PURE__*/
  messageDesc(file_Profile, 4);

/**
 * @generated from message com.stablemoney.api.broking.DematAccountDetail
 */
export type DematAccountDetail = Message<"com.stablemoney.api.broking.DematAccountDetail"> & {
  /**
   * @generated from field: string demat_id = 1;
   */
  dematId: string;

  /**
   * @generated from field: string broker_name = 2;
   */
  brokerName: string;
};

/**
 * Describes the message com.stablemoney.api.broking.DematAccountDetail.
 * Use `create(DematAccountDetailSchema)` to create a new message.
 */
export const DematAccountDetailSchema: GenMessage<DematAccountDetail> = /*@__PURE__*/
  messageDesc(file_Profile, 5);

/**
 * @generated from message com.stablemoney.api.broking.OnboardingModuleStatusData
 */
export type OnboardingModuleStatusData = Message<"com.stablemoney.api.broking.OnboardingModuleStatusData"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.OnboardingModule name = 1;
   */
  name: OnboardingModule;

  /**
   * @generated from field: bool status = 2;
   */
  status: boolean;
};

/**
 * Describes the message com.stablemoney.api.broking.OnboardingModuleStatusData.
 * Use `create(OnboardingModuleStatusDataSchema)` to create a new message.
 */
export const OnboardingModuleStatusDataSchema: GenMessage<OnboardingModuleStatusData> = /*@__PURE__*/
  messageDesc(file_Profile, 6);

/**
 * @generated from message com.stablemoney.api.broking.UpdateNameResponse
 */
export type UpdateNameResponse = Message<"com.stablemoney.api.broking.UpdateNameResponse"> & {
};

/**
 * Describes the message com.stablemoney.api.broking.UpdateNameResponse.
 * Use `create(UpdateNameResponseSchema)` to create a new message.
 */
export const UpdateNameResponseSchema: GenMessage<UpdateNameResponse> = /*@__PURE__*/
  messageDesc(file_Profile, 7);

/**
 * @generated from message com.stablemoney.api.broking.UpdateNameRequest
 */
export type UpdateNameRequest = Message<"com.stablemoney.api.broking.UpdateNameRequest"> & {
  /**
   * @generated from field: string first_name = 1;
   */
  firstName: string;

  /**
   * @generated from field: string last_name = 2;
   */
  lastName: string;

  /**
   * @generated from field: optional bool update_name = 3;
   */
  updateName?: boolean;
};

/**
 * Describes the message com.stablemoney.api.broking.UpdateNameRequest.
 * Use `create(UpdateNameRequestSchema)` to create a new message.
 */
export const UpdateNameRequestSchema: GenMessage<UpdateNameRequest> = /*@__PURE__*/
  messageDesc(file_Profile, 8);

/**
 * @generated from message com.stablemoney.api.broking.DeleteUserRequest
 */
export type DeleteUserRequest = Message<"com.stablemoney.api.broking.DeleteUserRequest"> & {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: string reason = 2;
   */
  reason: string;
};

/**
 * Describes the message com.stablemoney.api.broking.DeleteUserRequest.
 * Use `create(DeleteUserRequestSchema)` to create a new message.
 */
export const DeleteUserRequestSchema: GenMessage<DeleteUserRequest> = /*@__PURE__*/
  messageDesc(file_Profile, 9);

/**
 * @generated from message com.stablemoney.api.broking.DeleteUserResponse
 */
export type DeleteUserResponse = Message<"com.stablemoney.api.broking.DeleteUserResponse"> & {
};

/**
 * Describes the message com.stablemoney.api.broking.DeleteUserResponse.
 * Use `create(DeleteUserResponseSchema)` to create a new message.
 */
export const DeleteUserResponseSchema: GenMessage<DeleteUserResponse> = /*@__PURE__*/
  messageDesc(file_Profile, 10);

/**
 * @generated from message com.stablemoney.api.broking.UserOnboardingResponse
 */
export type UserOnboardingResponse = Message<"com.stablemoney.api.broking.UserOnboardingResponse"> & {
  /**
   * @generated from field: bool is_registered = 1;
   */
  isRegistered: boolean;

  /**
   * @generated from field: string status = 2;
   */
  status: string;
};

/**
 * Describes the message com.stablemoney.api.broking.UserOnboardingResponse.
 * Use `create(UserOnboardingResponseSchema)` to create a new message.
 */
export const UserOnboardingResponseSchema: GenMessage<UserOnboardingResponse> = /*@__PURE__*/
  messageDesc(file_Profile, 11);

/**
 * @generated from message com.stablemoney.api.broking.UserResponse
 */
export type UserResponse = Message<"com.stablemoney.api.broking.UserResponse"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: string phone_number = 3;
   */
  phoneNumber: string;

  /**
   * @generated from field: bool phone_verified = 4;
   */
  phoneVerified: boolean;

  /**
   * @generated from field: string email = 5;
   */
  email: string;

  /**
   * @generated from field: bool email_verified = 6;
   */
  emailVerified: boolean;

  /**
   * @generated from field: string profile_image_url = 8;
   */
  profileImageUrl: string;

  /**
   * @generated from field: string masked_email = 12;
   */
  maskedEmail: string;

  /**
   * @generated from field: string social_name = 13;
   */
  socialName: string;

  /**
   * @generated from field: string first_name = 14;
   */
  firstName: string;

  /**
   * @generated from field: string last_name = 15;
   */
  lastName: string;

  /**
   * @generated from field: string unverified_email = 16;
   */
  unverifiedEmail: string;

  /**
   * @generated from field: string created_at = 17;
   */
  createdAt: string;
};

/**
 * Describes the message com.stablemoney.api.broking.UserResponse.
 * Use `create(UserResponseSchema)` to create a new message.
 */
export const UserResponseSchema: GenMessage<UserResponse> = /*@__PURE__*/
  messageDesc(file_Profile, 12);

/**
 * @generated from message com.stablemoney.api.broking.GetCurrentAddressRequest
 */
export type GetCurrentAddressRequest = Message<"com.stablemoney.api.broking.GetCurrentAddressRequest"> & {
};

/**
 * Describes the message com.stablemoney.api.broking.GetCurrentAddressRequest.
 * Use `create(GetCurrentAddressRequestSchema)` to create a new message.
 */
export const GetCurrentAddressRequestSchema: GenMessage<GetCurrentAddressRequest> = /*@__PURE__*/
  messageDesc(file_Profile, 13);

/**
 * @generated from message com.stablemoney.api.broking.GetCurrentAddressResponse
 */
export type GetCurrentAddressResponse = Message<"com.stablemoney.api.broking.GetCurrentAddressResponse"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.AddressProto current_address = 1;
   */
  currentAddress?: AddressProto;
};

/**
 * Describes the message com.stablemoney.api.broking.GetCurrentAddressResponse.
 * Use `create(GetCurrentAddressResponseSchema)` to create a new message.
 */
export const GetCurrentAddressResponseSchema: GenMessage<GetCurrentAddressResponse> = /*@__PURE__*/
  messageDesc(file_Profile, 14);

/**
 * @generated from message com.stablemoney.api.broking.UserAnnualIncomeResponse
 */
export type UserAnnualIncomeResponse = Message<"com.stablemoney.api.broking.UserAnnualIncomeResponse"> & {
  /**
   * @generated from field: double annual_income = 1;
   */
  annualIncome: number;
};

/**
 * Describes the message com.stablemoney.api.broking.UserAnnualIncomeResponse.
 * Use `create(UserAnnualIncomeResponseSchema)` to create a new message.
 */
export const UserAnnualIncomeResponseSchema: GenMessage<UserAnnualIncomeResponse> = /*@__PURE__*/
  messageDesc(file_Profile, 15);

/**
 * @generated from enum com.stablemoney.api.broking.UserStatus
 */
export enum UserStatus {
  /**
   * @generated from enum value: USER_STATUS_UNKNOWN = 0;
   */
  USER_STATUS_UNKNOWN = 0,

  /**
   * @generated from enum value: REGISTRATION_PENDING = 1;
   */
  REGISTRATION_PENDING = 1,

  /**
   * @generated from enum value: ACTIVE = 2;
   */
  ACTIVE = 2,

  /**
   * @generated from enum value: DISABLED = 3;
   */
  DISABLED = 3,
}

/**
 * Describes the enum com.stablemoney.api.broking.UserStatus.
 */
export const UserStatusSchema: GenEnum<UserStatus> = /*@__PURE__*/
  enumDesc(file_Profile, 0);

/**
 * @generated from enum com.stablemoney.api.broking.ClientCategory
 */
export enum ClientCategory {
  /**
   * @generated from enum value: USER_CATEGORY_UNKNOWN = 0;
   */
  USER_CATEGORY_UNKNOWN = 0,

  /**
   * @generated from enum value: INDIVIDUAL = 1;
   */
  INDIVIDUAL = 1,

  /**
   * @generated from enum value: CORPORATE = 2;
   */
  CORPORATE = 2,

  /**
   * @generated from enum value: BROKER = 3;
   */
  BROKER = 3,

  /**
   * @generated from enum value: INSURANCE_COMPANY = 4;
   */
  INSURANCE_COMPANY = 4,

  /**
   * @generated from enum value: HUF = 5;
   */
  HUF = 5,
}

/**
 * Describes the enum com.stablemoney.api.broking.ClientCategory.
 */
export const ClientCategorySchema: GenEnum<ClientCategory> = /*@__PURE__*/
  enumDesc(file_Profile, 1);

