import Button from "@/components/ui/button/button";
import { trackEvent } from "@/utils/analytics";
import { getInvestmentsUrl } from "@/utils/routing";

export default function FirstTimeInvestor() {
  const handleYesClick = () => {
    localStorage.setItem("first_time_investor", "true");
    trackEvent("first_time_investor_yes_clicked");
  };

  const handleNoClick = () => {
    localStorage.setItem("first_time_investor", "false");
    trackEvent("first_time_investor_no_clicked");
  };

  return (
    <div className="flex h-full flex-col gap-4 p-5 md:gap-14 md:p-11">
      <h2 className="text-heading2 text-black-80 flex-1">
        Before we continue, have you ever invested in bonds before?
      </h2>
      <div className="flex flex-col gap-3">
        <Button
          onClick={handleYesClick}
          href={getInvestmentsUrl()}
          size="medium"
          variant="secondary"
          className="justify-start"
        >
          Yes, I have invested before
        </Button>
        <Button
          onClick={handleNoClick}
          href={getInvestmentsUrl()}
          size="medium"
          variant="secondary"
          className="justify-start"
        >
          No, this is my first time
        </Button>
      </div>
    </div>
  );
}
