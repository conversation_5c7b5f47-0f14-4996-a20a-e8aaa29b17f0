import {
  fromPromise,
  setup,
  assign,
  spawnChild,
  from<PERSON><PERSON>back,
  type DoneActorEvent,
} from "xstate";
import { getRpdStatus, getRpdUpiLinks } from "@/queries/bank-workflow";
import { createPollingMachine } from "@/machines/polling";
import { rpdStatusFetchLimit } from "@/config/limits";
import {
  StepName,
  WorkflowName,
  type ContinueWorkflowResponse,
} from "@/clients/gen/broking/WorkflowStep_pb";
import type { InitiateRpdResponse } from "@/clients/gen/broking/BankAccountVerification_pb";
import { getWorkflowStatus } from "@/queries/workflow";

type RpdStatus = Awaited<ReturnType<typeof getRpdStatus>>;
type RpdLinks = Awaited<ReturnType<typeof getRpdUpiLinks>>;
type WorkflowStatusResponse = Awaited<ReturnType<typeof getWorkflowStatus>>;

export const rpdMachine = setup({
  types: {
    context: {} as {
      workflowName: WorkflowName;
      rpdData?: InitiateRpdResponse;
      error?: unknown;
      nextStep?: ContinueWorkflowResponse;
    },
    input: {} as {
      workflowName: WorkflowName;
    },
    events: {} as
      | {
          type: "RETRY";
        }
      | {
          type: "START_POLLING";
        }
      | {
          type: "LIMIT_REACHED";
          error: unknown;
        }
      | DoneActorEvent<{ refId: string }>
      | DoneActorEvent<RpdLinks>
      | DoneActorEvent<WorkflowStatusResponse>
      | DoneActorEvent<RpdStatus>,
  },
  actors: {
    getWorkflowStatus: fromPromise(
      ({ input }: { input: { workflowName: WorkflowName } }) =>
        getWorkflowStatus(input.workflowName)
    ),
    getRpdLinks: fromPromise(
      ({ input }: { input: { workflowName: WorkflowName } }) =>
        getRpdUpiLinks(input.workflowName)
    ),
    polling: createPollingMachine<RpdStatus>("bank-rpd-polling", "evaluating"),
    checkForRefId: fromPromise(async () => {
      const urlParams = new URLSearchParams(window.location.search);
      const refId = urlParams.get("refId");
      return { refId };
    }),
    expiryHandler: fromCallback(({ sendBack }) => {
      const timer = setTimeout(() => {
        sendBack({ type: "RETRY" });
      }, 120000);
      return () => clearTimeout(timer);
    }),
  },
  actions: {
    setError: assign({
      error: ({ event }) => {
        if ("error" in event) return event.error;
      },
    }),
    assignNextStep: assign(({ event, context }) => {
      // RPD link fetch
      if (
        "output" in event &&
        "nextStep" in event.output &&
        typeof event.output.nextStep === "object"
      ) {
        return {
          ...context,
          nextStep: event.output.nextStep,
        };
      }
      if (
        "output" in event &&
        "nextStep" in event.output &&
        "workflowStatus" in event.output
      ) {
        return {
          ...context,
          nextStep: event.output,
        };
      }
      return context;
    }),
    assignRpdData: assign(({ event, context }) => {
      if (
        "output" in event &&
        "result" in event.output &&
        event.output.result.case === "initiateRpdResponse"
      ) {
        return {
          ...context,
          rpdData: event.output.result.value,
        };
      }
      return context;
    }),
    assignRpdError: assign(({ event, context }) => {
      if ("output" in event && "message" in event.output) {
        return {
          ...context,
          error: new Error(event.output.message),
        };
      }
      return context;
    }),
  },
  guards: {
    isRefIdPresent: ({ event }) =>
      "output" in event && "refId" in event.output && !!event.output.refId,
    hasDifferentStep: ({ event }) => {
      if (
        "output" in event &&
        "nextStep" in event.output &&
        typeof event.output.nextStep === "object"
      ) {
        const nextStep = event.output.nextStep?.nextStep;
        return !!nextStep && nextStep !== StepName.BANK_ACCOUNT;
      }
      if (
        "output" in event &&
        "nextStep" in event.output &&
        "workflowStatus" in event.output
      ) {
        return event.output.nextStep !== StepName.BANK_ACCOUNT;
      }
      return false;
    },
    rpdSuccessful({ event }) {
      return (
        "output" in event && "verified" in event.output && event.output.verified
      );
    },
  },
}).createMachine({
  id: "rpd",
  initial: "starting",
  context: ({ input }) => ({
    workflowName: input.workflowName,
  }),
  on: {
    RETRY: ".fetchingLinks",
  },
  entry: [spawnChild("expiryHandler")],
  states: {
    starting: {
      invoke: {
        src: "checkForRefId",
        onDone: [
          {
            target: "polling",
            guard: "isRefIdPresent",
          },
          { target: "fetchingWorkflowStatus" },
        ],
        onError: {
          target: "error",
          actions: "setError",
        },
      },
    },
    fetchingWorkflowStatus: {
      invoke: {
        src: "getWorkflowStatus",
        input: ({ context }) => ({ workflowName: context.workflowName }),
        onDone: [
          {
            target: "completed",
            guard: "hasDifferentStep",
            actions: "assignNextStep",
          },
          { target: "fetchingLinks" },
        ],
        onError: {
          target: "error",
          actions: "setError",
        },
      },
    },
    fetchingLinks: {
      invoke: {
        src: "getRpdLinks",
        input: ({ context }) => ({ workflowName: context.workflowName }),
        onDone: [
          {
            target: "completed",
            guard: "hasDifferentStep",
            actions: "assignNextStep",
          },
          {
            target: "fetchedLinks",
            actions: "assignRpdData",
          },
        ],
        onError: {
          target: "error",
          actions: "setError",
        },
      },
    },
    fetchedLinks: {
      on: {
        START_POLLING: "polling",
      },
    },
    polling: {
      invoke: {
        src: "polling",
        input: ({ context }) => ({
          runner: () =>
            getRpdStatus(context.rpdData!.refId, context.workflowName),
          guard: (result) =>
            ["SUCCESS", "FAILED", "EXPIRED"].includes(result.status),
          maxCount: rpdStatusFetchLimit,
          interval: 5000,
        }),
        onDone: [
          {
            target: "success",
            guard: "rpdSuccessful",
            actions: "assignNextStep",
          },
          {
            target: "error",
            actions: "assignRpdError",
          },
        ],
      },
      on: {
        LIMIT_REACHED: {
          target: "error",
          actions: "setError",
        },
      },
    },
    success: {
      after: {
        1000: "completed",
      },
    },
    completed: {
      type: "final",
    },
    error: {},
  },
});
