import type { BankVerificationStatusResponse } from "@/clients/gen/broking/WorkflowStep_pb";
import Button from "@/components/ui/button/button";
import CountdownTimer from "@/components/ui/countdown-timer";
import FloatingFooterContent from "@/components/ui/floating-footer-content";
import Head from "@/components/ui/head";
import useNavigate from "@/hooks/navigate";
import { usePolling } from "@/machines/polling";
import { getRpdStatus } from "@/queries/bank-workflow";
import {
  getPathForWorkflowStep,
  getWorkflowNameFromSlug,
} from "@/utils/workflow-routes";
import { DotLottieReact } from "@lottiefiles/dotlottie-react";
import { useCallback, useEffect } from "react";
import { useParams } from "react-router";
import successImage from "@/assets/images/icons/purple-tick.svg";
import ErrorPage from "@/components/functional/error-page";

export default function RpdStatusPage() {
  const navigate = useNavigate();
  const params = useParams();
  const { slug, refId } = params;
  const upiIntent = new URLSearchParams(window.location.search).get(
    "upiIntent"
  )!;
  const workflowName = getWorkflowNameFromSlug(slug!);
  const { start, status, response } =
    usePolling<BankVerificationStatusResponse>("bank-rpd-polling");
  useEffect(() => {
    start({
      runner: () => getRpdStatus(refId!, workflowName!),
      guard: (result) =>
        ["SUCCESS", "FAILED", "EXPIRED"].includes(result.status),
      maxCount: 24,
      interval: 5000,
    });
  }, [refId, start, workflowName]);
  useEffect(() => {
    const timer = setTimeout(() => {
      window.location.href = upiIntent;
    }, 1000);
    return () => clearTimeout(timer);
  }, [upiIntent]);

  const retry = useCallback(() => {
    navigate(`/workflow/${slug}/bank-rpd`, { replace: true });
  }, [navigate, slug]);

  if (status === "completed") {
    if (response?.verified) {
      const nextUrl = getPathForWorkflowStep(workflowName, response?.nextStep);
      return <SuccessPage nextUrl={nextUrl} />;
    }
    return (
      <ErrorPage
        title={response?.message || "Bank verification failed"}
        description="Please try again. If the issue persists, contact our support team."
        onRetry={retry}
      />
    );
  }

  return (
    <>
      <Head title="Bank Verification - Processing" />
      <div className="app-bar-height-top-padding floating-footer-padding fixed inset-0 flex flex-col items-center justify-center gap-6 p-5 text-center">
        <div className="h-25">
          <DotLottieReact
            src="https://assets.stablemoney.in/web-frontend/website/verifyingLottie.json"
            autoplay
            loop
            height={100}
          />
        </div>
        <CountdownTimer
          duration={120}
          onFinish={retry}
          autoStart={true}
          label="Time remaining"
          className="mb-4"
        />
        <div className="mx-auto max-w-sm space-y-3 text-center">
          <h1 className="text-heading2">Verifying bank account</h1>
          <p className="text-body1 text-black-50">
            Your bank is being verified with UPI. You will be automatically
            redirected once completed.
          </p>
        </div>
        <FloatingFooterContent>
          <p className="text-body2 text-black-50 mb-2 text-center">
            Close this if you have not logged into any UPI apps
          </p>
          <Button onClick={retry}>Close</Button>
        </FloatingFooterContent>
      </div>
    </>
  );
}

function SuccessPage({ nextUrl }: { nextUrl: string }) {
  const navigate = useNavigate();
  useEffect(() => {
    const timer = setTimeout(() => {
      navigate(nextUrl, { replace: true });
    }, 1000);
    return () => clearTimeout(timer);
  }, [navigate, nextUrl]);

  return (
    <>
      <Head title="Bank Verification - Success" />
      <div className="flex flex-col items-center justify-center space-y-6 p-5 py-12">
        <div className="flex flex-col items-center space-y-4">
          <img src={successImage} alt="Success" className="h-12 w-12" />
          <div className="space-y-2 text-center">
            <h1 className="text-heading1 text-green-600">
              Bank account verified!
            </h1>
            <p className="text-black-50 text-body1">
              Redirecting you to the next step...
            </p>
          </div>
        </div>
      </div>
      <FloatingFooterContent>
        <Button href={nextUrl} replace>
          Continue
        </Button>
      </FloatingFooterContent>
    </>
  );
}
