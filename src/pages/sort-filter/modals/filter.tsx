import Button from "@/components/ui/button/button";
import FilterTray from "../components/sort-filters/filter-tray";
import smsFilterIcon from "@/assets/images/icons/sms_filter_icon.svg";
import type {
  FilterOptionValue,
  FilterSortConfigResponse,
} from "@/clients/gen/broking/FilterSearch_pb";
import type { AppliedFilter } from "../machine";
import { Drawer } from "vaul";
import { useState } from "react";
import closeIcon from "@/assets/images/icons/close.svg";
import { trackEvent } from "@/utils/analytics";
import { EventProps, FilterSearchEvents } from "../events";

interface FilterButtonProps {
  filterConfig?: FilterSortConfigResponse;
  handleFilterChange: (
    filter: {
      key: string;
      filters: FilterOptionValue;
      label: string;
    },
    isSelected: boolean,
    selectedIndex: number
  ) => void;
  appliedFilters: AppliedFilter[];
  handleClearFilters: () => void;
  isLoadingResults: boolean;
  currentPage: number;
  totalResults: number;
}

const FilterButton = ({
  appliedFilters,
  handleFilterChange,
  handleClearFilters,
  totalResults,
  filterConfig,
  currentPage,
  isLoadingResults,
}: FilterButtonProps) => {
  const [open, setOpen] = useState(false);
  const [isFilterChanged, setIsFilterChanged] = useState(false);

  const handleDrawerToggle = () => {
    setOpen(!open);
    trackEvent(
      open
        ? FilterSearchEvents.bondsSearchFilterFilterBottomSheetDismissed
        : FilterSearchEvents.bondsSearchFilterFilterBottomSheetOpened,
      {
        [EventProps.filterCount]: appliedFilters.length,
      }
    );
  };
  return (
    <Drawer.Root dismissible={false} open={open}>
      <Button
        className="h-10! shrink-1 rounded-lg!"
        onClick={handleDrawerToggle}
      >
        <span className="flex items-center gap-1">
          <img src={smsFilterIcon} alt="filter" />
          <p className="text-title-all-caps">FILTERS </p>
          {isFilterChanged && (
            <span className="bg-neon-green h-1 w-1 rounded-[1px]"></span>
          )}
        </span>
      </Button>
      <Drawer.Portal>
        <Drawer.Overlay
          className="bg-black-50 fixed inset-0 z-40"
          onClick={handleDrawerToggle}
        />
        <Drawer.Content className="fixed inset-x-0 bottom-0 z-50 flex flex-col rounded-t-xl bg-white shadow-lg">
          <div className="max-h-[80dvh] flex-1 overflow-y-auto">
            <div className="sticky top-0 z-1 rounded-t-xl bg-white px-5">
              <div className="py-4">
                <Drawer.Handle className="bg-black-20 h-1.5 w-24 rounded-full" />
              </div>
              <div className="sticky top-0 flex items-center justify-between pb-3">
                <h2 className="text-heading4 text-black-80 font-medium">
                  {filterConfig?.filter?.title}
                </h2>
                <button onClick={handleDrawerToggle}>
                  <img src={closeIcon} alt="Close" className="w-6" />
                </button>
              </div>
              <hr className="border-black-10 border-t" />
            </div>
            <FilterTray
              filterConfig={filterConfig?.filter}
              totalResults={totalResults}
              toggleFilterPanel={() => setOpen(false)}
              appliedFilters={appliedFilters}
              handleFilterChange={(filter, isSelected, selectedIndex) => {
                if (!isFilterChanged) {
                  setIsFilterChanged(true);
                }
                handleFilterChange(filter, isSelected, selectedIndex);
                trackEvent(FilterSearchEvents.bondsSearchSuggestedFilterClick, {
                  [EventProps.option]: filter.key,
                  [EventProps.optionText]: filter.label,
                  [EventProps.selectedOptions]: appliedFilters,
                });
              }}
              handleClearFilters={handleClearFilters}
              isLoading={isLoadingResults && currentPage === 0}
            />
          </div>
        </Drawer.Content>
      </Drawer.Portal>
    </Drawer.Root>
  );
};

export default FilterButton;
