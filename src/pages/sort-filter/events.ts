export const FilterSearchEvents = {
  searchAndFilterbondsPage: "bonds_search_filter_page_viewed",
  searchAndFilterConfig: "bonds_search_filter_page_config", // not done
  bondsSearchFilterscrolledToTop: "bonds_search_filter_scrolled_to_top",
  searchAndFilterSwipeRefreshed: "bonds_search_filter_swipe_refreshed", // not done
  bondsSearchFilterQuickFilterTapped: "bonds_search_filter_quick_filter_tapped",
  bondsSearchFilterQuickClearAll: "bonds_search_filter_quick_clear_all_tapped",
  bondsSearchFilterQuickRemoved: "bonds_search_filter_quick_item_removed",
  bondsSearchFilterRateFormatChanged: "bonds_search_filter_rate_format_changed", //no need toggling of column
  bondsSearchFilterRateFormatInfoBottomSheet:
    "bonds_search_filter_rate_format_info_bottom_sheet", //no need toggling of column
  bondsSearchFilterResultItemClicked: "bonds_search_filter_result_item_clicked",
  bondsSearchFilterResultIconClicked: "bonds_search_filter_result_icon_clicked",
  bondsSearchMultiFilterClick: "bonds_search_filter_multi_item_clicked", //not able to understand
  bondsSearchSuggestedFilterClick: "bonds_search_filter_item_clicked", // not done - dielema
  bondsSearchFilterSortBottomSheetDismissed:
    "bonds_search_filter_sort_bottom_sheet_dismissed",
  bondsSearchFilterSortOptionClicked: "bonds_search_filter_sort_option_clicked",
  bondsSearchFilterSortBottomSheetOpened:
    "bonds_search_filter_sort_bottom_sheet_opened",
  bondsSearchFilterFilterBottomSheetOpened:
    "bonds_search_filter_filter_bottom_sheet_opened",
  bondsSearchFilterFilterBottomSheetDismissed:
    "bonds_search_filter_filter_bottom_sheet_dismissed",
  bondsSearchFilterClearAll: "bonds_search_filter_clear_all_clicked",
  bondsSearchFilterFilterBottomSheetSubmitted:
    "bonds_search_filter_filter_bottom_sheet_submitted",
  searchAndFilterResultFetched: "bonds_search_filter_result_fetched",
  searchAndFilterDropDownOpened: "bonds_search_filter_dropdown_opened",
  bondsSearchFilterExpanded: "bonds_search_filter_item_expanded", // not done
};

export const EventProps = {
  option: "option",
  optionText: "option_text",
  selectedOptions: "selected_options",
  totalOptions: "total_options",
  sortOption: "sort_option",
  sortOptionText: "sort_option_text",
  resultCount: "result_count",
  filterCount: "filter_count",
  filterName: "filter_name",
  filterNameText: "filter_name_text",
  isExpanded: "is_expanded",
  bondName: "bond_name",
  isin: "isin",
  investabilityStatus: "investability_status",
  bondXirr: "bond_xirr",
  rating: "rating",
  countOfFilteredOptions: "count_of_filtered_options",
  currentIndex: "current_index",
};
