import type {
  FilterConfig,
  FilterItem,
  FilterOptionValue,
} from "@/clients/gen/broking/FilterSearch_pb";
import { FilterType } from "@/clients/gen/broking/FilterSearch_pb";
import Checkbox from "@/components/ui/checkbox/checkbox";
import Switch from "@/components/ui/switch/switch";
import Chip from "@/components/ui/chip/chip";
import Accordion from "@/components/ui/accordion/accordion";
import AccordionItem from "@/components/ui/accordion/accordion-item";
import Button from "@/components/ui/button/button";
import type { AppliedFilter, FilterOptionValueWithLabel } from "../../machine";
import CircularProgressIndicator from "@/components/icons/circular-progress-indicator";
import { trackEvent } from "@/utils/analytics";
import { EventProps, FilterSearchEvents } from "../../events";
import { t } from "i18next";

interface FilterTrayProps {
  filterConfig?: FilterConfig;
  totalResults: number;
  toggleFilterPanel: () => void;
  handleFilterChange: (
    filter: {
      key: string;
      filters: FilterOptionValue;
      label: string;
    },
    isSelected: boolean,
    selectedIndex: number
  ) => void;
  appliedFilters: AppliedFilter[];
  handleClearFilters: () => void;
  isLoading: boolean;
}

interface FilterItemRendererProps {
  item: FilterItem;
  handleFilter: (
    filters: FilterOptionValue,
    isSelected: boolean,
    selectedIndex: number
  ) => void;
  appliedFilters: AppliedFilter[];
}

export const FilterItemRenderer = ({
  item,
  handleFilter,
  appliedFilters,
}: FilterItemRendererProps) => {
  const renderFilterContent = () => {
    switch (item.type) {
      case FilterType.MULTI_SELECT_PILLS:
        return (
          <div className="mt-3 flex flex-wrap gap-2">
            {item.options.map((option, index) => (
              <Chip
                key={`${item.key}-${index}`}
                variant="outlined"
                selected={appliedFilters.some(
                  (filter) =>
                    filter.key === item.key &&
                    filter.filters.includes(
                      option.optionValue as FilterOptionValueWithLabel
                    )
                )}
                size="medium"
                onClick={(value) => {
                  handleFilter(option.optionValue!, value as boolean, index);
                }}
                className={item.options.length === 3 ? "flex-1" : ""}
              >
                <div className="flex flex-col items-center justify-center gap-0.5">
                  <p className="text-black-80 text-body1">{option.label}</p>
                  {option.labelDescription && (
                    <p className="text-black-80 text-body2">
                      {t(option.labelDescription)}
                    </p>
                  )}
                </div>
              </Chip>
            ))}
          </div>
        );
      case FilterType.RANGE_MULTI_SELECT:
      case FilterType.MULTI_SELECT:
        return (
          <div className="mt-3 space-y-3">
            {item.options.map((option, index) => (
              <Checkbox
                key={`${item.key}-${index}`}
                name={`${item.key}-${index}`}
                checked={appliedFilters.some(
                  (filter) =>
                    filter.key === item.key &&
                    filter.filters.includes(
                      option.optionValue as FilterOptionValueWithLabel
                    )
                )}
                className="text-black-80 flex-row-reverse justify-between"
                onChange={(event) => {
                  handleFilter(
                    option.optionValue!,
                    (event.target as HTMLInputElement).checked,
                    index
                  );
                }}
              >
                <p className="text-body1">{option.label}</p>
              </Checkbox>
            ))}
          </div>
        );

      case FilterType.MULTI_SELECT_WITH_ICON:
        return (
          <div className="mt-3 flex flex-wrap gap-2.5">
            {item.options.map((option, index) => (
              <div
                key={`${item.key}-${index}`}
                className="text-body1 flex items-center gap-2"
              >
                <Chip
                  selected={appliedFilters.some(
                    (filter) =>
                      filter.key === item.key &&
                      filter.filters.includes(
                        option.optionValue as FilterOptionValueWithLabel
                      )
                  )}
                  onClick={(isSelected) => {
                    handleFilter(
                      option.optionValue!,
                      isSelected as boolean,
                      index
                    );
                  }}
                  variant="outlined"
                  size="medium"
                >
                  <img src={option.icon} className="h-4 w-4 object-contain" />
                  {option.label}
                </Chip>
              </div>
            ))}
          </div>
        );

      case FilterType.BOOLEAN_SELECT:
        return null;

      default:
        return (
          <div className="mt-3 space-y-3">
            {item.options.map((option, index) => (
              <Checkbox
                key={`${item.key}-${index}`}
                name={`${item.key}-${index}`}
                defaultChecked={option.isSelected}
                className="text-body1 flex-row-reverse justify-between"
                onChange={(event) => {
                  handleFilter(
                    option.optionValue!,
                    (event.target as HTMLInputElement).checked,
                    index
                  );
                }}
              >
                {option.label}
              </Checkbox>
            ))}
          </div>
        );
    }
  };

  if (item.type === FilterType.BOOLEAN_SELECT) {
    return (
      <div className="py-5">
        <div className="flex items-center justify-between">
          <span className="text-black-80 text-heading4">{item.label}</span>
          <Switch
            name={item.key}
            checked={appliedFilters.some((filter) => filter.key === item.key)}
            onChange={(event) => {
              if (item.options[0]?.optionValue) {
                handleFilter(
                  item.options[0].optionValue,
                  (event.target as HTMLInputElement).checked,
                  0
                );
              }
            }}
          />
        </div>
      </div>
    );
  }

  if (item.isCollapsible) {
    return (
      <div className="">
        <Accordion
          defaultValue={item.isCollapsed ? [] : [item.key]}
          className="m-0! p-0!"
        >
          <AccordionItem
            id={item.key}
            label={
              <h4 className="text-heading4 text-black-80">{item.label}</h4>
            }
            className="py-5"
          >
            {renderFilterContent()}
          </AccordionItem>
        </Accordion>
      </div>
    );
  }

  return (
    <div className="py-5">
      <div className="space-y-2">
        <h4 className="text-heading4 text-black-80">{item.label}</h4>
        {renderFilterContent()}
      </div>
    </div>
  );
};

const FilterTray = ({
  filterConfig,
  totalResults,
  toggleFilterPanel,
  handleFilterChange,
  appliedFilters,
  handleClearFilters,
  isLoading,
}: FilterTrayProps) => {
  return (
    <>
      <div className="flex flex-col px-5 pb-25">
        {filterConfig?.items.map((item, index) => (
          <>
            <FilterItemRenderer
              key={`${item.key}-${index}`}
              item={item}
              handleFilter={(filters, isSelected, selectedIndex) =>
                handleFilterChange(
                  {
                    key: item.key,
                    filters,
                    label: item.options[selectedIndex].label,
                  },
                  isSelected,
                  selectedIndex
                )
              }
              appliedFilters={appliedFilters}
            />
            <hr className="border-black-10 border-t" />
          </>
        ))}
      </div>
      <div className="bg-bg fixed right-0 bottom-0 left-0 flex items-center justify-between p-5">
        {appliedFilters.length > 0 ? (
          <>
            <span
              onClick={() => {
                handleClearFilters();
                trackEvent(FilterSearchEvents.bondsSearchFilterClearAll, {
                  [EventProps.selectedOptions]: appliedFilters,
                });
              }}
              className="text-body1 shrink-0 underline [text-decoration-color:#BBB] [text-decoration-style:dotted] [text-decoration-thickness:9.5%] [text-underline-offset:40%] [text-decoration-skip-ink:none] [text-underline-position:from-font]"
            >
              Clear all
            </span>
          </>
        ) : (
          <p className="text-body1 text-black-80 underline [text-decoration-color:#BBB] [text-decoration-style:dotted] [text-decoration-thickness:9.5%] [text-underline-offset:40%] [text-decoration-skip-ink:none] [text-underline-position:from-font]">
            No filters selected
          </p>
        )}

        <div className="w-1/2">
          <Button
            className="text-heading4 shrink-1"
            onClick={() => {
              trackEvent(
                FilterSearchEvents.bondsSearchFilterFilterBottomSheetSubmitted,
                {
                  [EventProps.selectedOptions]: appliedFilters,
                  [EventProps.countOfFilteredOptions]: totalResults,
                }
              );
              toggleFilterPanel();
            }}
          >
            {isLoading ? (
              <CircularProgressIndicator />
            ) : (
              `View ${totalResults} results`
            )}
          </Button>
        </div>
      </div>
    </>
  );
};

export default FilterTray;
