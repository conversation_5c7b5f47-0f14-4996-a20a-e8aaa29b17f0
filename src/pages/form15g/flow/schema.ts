import * as yup from "yup";

export const validationSchema = yup.object({
  annualIncome: yup
    .string()
    .required("Annual income is required")
    .test("is-number", "Annual income must be in number", (value) => {
      if (!value) return false;
      const numValue = parseFloat(value);
      return !isNaN(numValue);
    }),
});

export type FormValues = yup.InferType<typeof validationSchema>;
