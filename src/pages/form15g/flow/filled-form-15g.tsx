import formEnvelopeImage from "@/assets/images/illustrations/form-envelope.webp";
import Button from "@/components/ui/button/button";
import FloatingFooterContent from "@/components/ui/floating-footer-content";
import copyIcon from "@/assets/images/icons/copy.svg";
import pdfIcon from "@/assets/images/icons/file-pdf.svg";
import { trackEvent } from "@/utils/analytics";
import * as native from "@/utils/native-integration";
import { useSearchParams } from "react-router";
import { useQuery } from "@tanstack/react-query";
import { getForm15G } from "@/queries/form15-flow";

const FilledForm15G = () => {
  const [searchParams] = useSearchParams();
  const isin = searchParams.get("isin")!;
  const annualIncome = searchParams.get("annualIncome")!;

  const getForm = useQuery({
    queryKey: ["form15G"],
    queryFn: () =>
      getForm15G({
        annualIncome: parseInt(annualIncome),
        isin,
      }),
    retry(failureCount) {
      return failureCount < 2;
    },
    retryDelay: 500,
  });

  const handleEmailSend = () => {
    trackEvent("form_15g_email_send_clicked");
    if (native.sendEmail.isSupported()) {
      let url = "";
      if (getForm.data?.form?.fileContent) {
        const blob = new Blob([getForm.data?.form?.fileContent], {
          type: "application/pdf",
        });
        url = URL.createObjectURL(blob);
      }
      native.sendEmail.shareEmail(
        "<EMAIL>",
        "Send Form 15G",
        "Body Form 15G",
        url
      );
    }
  };

  const handleDownload = () => {
    trackEvent("form_15g_download_clicked");
    if (getForm.data?.form?.fileContent) {
      const form = getForm.data?.form;
      const blob = new Blob([form.fileContent], { type: "application/pdf" });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = form.fileName;
      a.click();
      trackEvent("form15g_download_success", {
        filename: form.fileName,
      });
    }
  };

  const handleCopy = () => {
    navigator.clipboard?.writeText(getForm.data?.issuerEmailId ?? "");
  };

  return (
    <>
      <div className="floating-footer-padding flex min-h-[calc(100dvh_-_var(--app-bar-height))] flex-col items-center justify-center gap-8 px-5">
        <p className="text-heading3 text-black-50">
          Your <span className="text-black-80">pre-filled Form 15B</span> is
          ready
        </p>
        <div className="relative">
          <img
            src={formEnvelopeImage}
            alt="form envelope"
            className="drop-shadow-md"
          />
          <div className="absolute right-0 bottom-7.5 left-0 flex flex-col items-center gap-4">
            <div className="flex flex-col items-center justify-center gap-3">
              <img src={pdfIcon} alt="" />
              <p className="text-heading4 text-black-80">
                {getForm.data?.form?.fileName}
              </p>
            </div>
            <p
              className="text-body1 text-black-50 underline underline-offset-3"
              onClick={handleDownload}
            >
              View Form 15G
            </p>
          </div>
        </div>
      </div>
      <FloatingFooterContent>
        <div className="flex flex-col gap-4">
          <div className="flex items-center justify-center gap-1">
            <p className="text-body1 text-black-60">
              Copy Navi Finserv's email ID
            </p>
            <img src={copyIcon} alt="copy icon" onClick={handleCopy} />
          </div>
          <Button onClick={handleEmailSend}>Send to Navi Finserv</Button>
        </div>
      </FloatingFooterContent>
    </>
  );
};

export default FilledForm15G;
