import FormPage from "@/components/bonds/form-page";
import Button from "@/components/ui/button/button";
import Head from "@/components/ui/head";
import { withErrorToast } from "@/utils/errors";
import { Formik, Form } from "formik";
import FormFields from "./fields";
import { validationSchema, type FormValues } from "./schema";
import clsx from "clsx";
import { useNavigate, useParams } from "react-router";

const Form15gFlow = () => {
  const { isin } = useParams<{ isin: string }>();
  const navigate = useNavigate();
  return (
    <>
      <Head title="Annual Income details" />
      <Formik
        initialValues={
          {
            annualIncome: "",
          } as FormValues
        }
        initialStatus="invalid"
        validateOnMount
        validationSchema={validationSchema}
        onSubmit={withErrorToast(async function handleSubmit(values) {
          navigate(
            `/tax/filled-form-15g?isin=${isin}&annualIncome=${values.annualIncome}`
          );
        })}
      >
        {(formState) => {
          return (
            <Form id="annual-income-form">
              <FormPage
                title="Annual income for Form 15G"
                description="It is required to generate your pre-filled Form 15G"
                footer={
                  <div className="space-y-3">
                    <Button
                      type="submit"
                      form="annual-income-form"
                      loading={formState.isSubmitting}
                      disabled={
                        parseInt(formState.values.annualIncome) > 1000000
                      }
                    >
                      Proceed
                    </Button>
                  </div>
                }
              >
                <FormFields {...formState} />
                <p
                  className={clsx(
                    "text-body1 text-black-50",
                    parseInt(formState.values.annualIncome) > 1000000 &&
                      "text-red"
                  )}
                >
                  TDS exemption with 15G is allowed for annual income below ₹10
                  lakh
                </p>
              </FormPage>
            </Form>
          );
        }}
      </Formik>
    </>
  );
};

export default Form15gFlow;
