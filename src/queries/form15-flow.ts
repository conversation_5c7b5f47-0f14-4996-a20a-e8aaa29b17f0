import { request } from "@/clients/broking-api";
import {
  Issuer15GFormRequestSchema,
  Issuer15GFormResponseSchema,
} from "@/clients/gen/broking/Order_pb";
import { UserAnnualIncomeResponseSchema } from "@/clients/gen/broking/Profile_pb";
import { create, toBinary } from "@bufbuild/protobuf";

export async function getAnnualIncome() {
  return request({
    method: "GET",
    url: `/v1/user/fy-income`,
    responseSchema: UserAnnualIncomeResponseSchema,
  });
}

export async function getForm15G({
  annualIncome,
  isin,
}: {
  annualIncome: number;
  isin: string;
}) {
  return request({
    method: "POST",
    url: `/v1/forms/15g/issuer`,
    data: toBinary(
      Issuer15GFormRequestSchema,
      create(Issuer15GFormRequestSchema, {
        annualIncome: annualIncome,
        isin: isin,
      })
    ),
    responseSchema: Issuer15GFormResponseSchema,
  });
}
