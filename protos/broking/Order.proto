syntax = "proto3";

package com.stablemoney.api.broking;
option java_package = "com.stablemoney.api.broking";
option java_multiple_files = true;
import "BondDetails.proto";

message PlaceOrderRequest {
  option deprecated = true;
  string bond_offering_details_id = 1 ;
  int32 quantity = 2;
}

message PlaceOrderRequestV2 {
  repeated PlaceOrderRequestItem place_order_request_items = 1;
}

message PlaceOrderRequestItem {
  string bond_detail_id = 1 ;
  int32 quantity = 2;
}

message StatusNode {
  bool status = 1;
  string date_time = 2;
  optional string description = 3;
}

message OrderData {
  string id = 1;                                                    // order level
  string bond_name = 2;                                             // item level
  string bond_issuer_logo = 3;                                      // item level
  int32 quantity = 4;                                               // item level
  double total_consideration = 5;                                   // order level
  string status = 6;                                                // order level
  string order_type = 7;                                            // order level
  string bond_offering_id = 8;                                      // item level
  double ytm = 9;                                                   // item level
  string bond_issuer_name = 10;                                     // item level
  string tenure = 11;                                               // item level
  string expected_returns = 12;                                     // item level
  bool is_order_settled = 13;                                       // order  level
  StatusNode order_placed = 14;                                     // item level
  StatusNode payment = 15;                                          // item level
  StatusNode settlement = 16;                                       // item level
  string order_receipt_url = 17;                                    // item level
  string deal_sheet_url = 18;                                       // item level
  string payment_url = 19;                                          // NSE payment url, item level
  string order_number = 20;                                         // NSE order number, item level
  string created_at = 21;                                           // order level
  repeated InterestPaymentSchedule repayment_schedule = 22;         // item level
  string payment_gateway = 23;                                            // order level
  StatusNode trade_placed = 24;                                             // item level
  string maturity_date = 25;                                            // dd-mm-yyyy
  string heading = 26;                                            // item level
  bool is_bond_investable = 27;                                             // item level
  string settlement_date = 28;                                            // dd-mm-yyyy
  optional string bond_issuer_id = 29;                                            // item level
  optional string bond_issuer_color = 30;                                             // item level
}

message OrderDataV2 {
  string id = 1;
  double total_consideration = 2;
  string status = 3; // cumilative state of each item
  string order_type = 4; // buy / sell
  bool is_order_settled = 5; // cumulative settlement state of each item
  string payment_gateway = 6;
  repeated OrderItemData order_item_data = 7;
}

message OrderItemData {
  string bond_name = 1;
  string status = 2;
  string bond_issuer_logo = 3;
  int32 quantity = 4;
  string bond_offering_id = 5;
  double ytm = 6;
  string bond_issuer_name = 7;
  string tenure = 8;
  StatusNode order_placed = 9;
  StatusNode payment = 10;
  StatusNode settlement = 11;
  string expected_returns = 12;
  string order_receipt_url = 13;
  string deal_sheet_url = 14;
  string order_number = 15; // NSE order number
  string created_at = 16;
  repeated InterestPaymentSchedule repayment_schedule = 17;
  StatusNode trade_placed = 18;
  string heading = 19;
  bool is_bond_investable = 20;
  string bond_issuer_id = 21;
  string bond_issuer_color =  22;
  bool is_settled = 23;
  string maturity_date = 24;
  string settlement_date = 25;
}

message InvestmentSummary {
  OrderData order_data = 1;
  InterestPayoutDetails interest_payout_details = 2;
}

message InvestmentSummariesRequest {
  string user_id = 1;
}

message InvestmentSummariesResponse {
  repeated InvestmentSummary investment_summaries = 1;
}

message PlaceOrderResponse {
  string order_id = 1;
  OrderData order = 2;
  string payment_gateway = 4;
}

message PlaceOrderResponseV2 {
  string order_id = 1;
  OrderDataV2 order_data = 2;
  string payment_gateway = 4;
  optional bool is_kyc_done = 5;
}

message PendingOrderItem {
  string order_id = 1;
  double amount = 2;
  int32 quantity = 3;
  string created_at = 4;
  string expire_by = 5;
  string bond_id = 6;
  string bond_logo = 7;
  string bond_name = 8;
  string payment_gateway = 9;
}

message PendingOrdersResponse {
  repeated PendingOrderItem items = 1;
}

enum PaymentType {
  UNKNOWN_PAYMENT_TYPE = 0;
  UPI = 1;
  NET_BANKING = 2;
}

message ProcessPaymentRequest {
  string order_id = 1;
  optional PaymentType payment_type = 2;
  optional string upi_id = 3;
}

message ProcessPaymentResponse {
  string status = 1;
  string payment_url = 2;
  string upi_id = 3;
  string order_id = 4;
  double payable_amount = 5;
  string payment_session_id = 6;
}

message PaymentStatusResponse {
  string order_id = 1;
  string status = 2;
  string bond_name = 3;
  int32 quantity = 4;
  double payable_amount = 5;
  string payment_time = 6;
  string settlement_date = 7;
}

message OrdersResponse {
  repeated OrderData orders = 1;
}

message InvestmentDetailsRequest {
  string order_id = 1;
  string user_id = 2;
}

message InvestmentDetailsResponse {
  OrderData orderData = 1;
  optional InvestmentNomineeDetails nominee_details = 2;
  optional WithdrawalBankAccountDetails withdrawal_bank_account_details = 3;
  optional InterestPayoutDetails interest_payout_details = 4;
  optional DematAccountDetails demat_account_details = 5;
}

message BondsNetworthSummaryResponse {
  double total_investment = 1;
  double current_gains = 2;
  int32 number_of_orders = 3;
  int32 number_of_matured_payments = 4;
  double total_amount_for_matured_payments = 5;
}

message UserNetworthResponse {
  string user_id = 1;
  double total_investment = 2;
  double current_gains = 3;
}

message AllUserNetworthResponse {
  repeated UserNetworthResponse user_net_worth = 1;
}


message InvestmentNomineeDetails {
  string nominee_name = 1;
  string nominee_relation = 2;
  string heading = 3;
}

message WithdrawalBankAccountDetails {
  string bank_name = 1;
  string account_number = 2;
  string ifsc_code = 3;
  string heading = 4;
}

message InterestPayoutDetails {
  double realised_gains = 1;
  double unrealised_gains = 2;
  string next_payout_date = 3; //dd-mm-yyyy
  string heading = 4;
}

message DematAccountDetails {
  string demat_number = 1;
  string demat_provider = 2;
  string heading = 3;
}

message Filled15GFormFile {
  string file_name = 1;
  bytes file_content = 2;
  string content_type = 3;
}

message Filled15GFormList {
  repeated Filled15GFormFile forms = 1;
}

message Issuer15GFormResponse {
  Filled15GFormFile form = 1;
  string issuer_name = 2;
  string issuer_email_id = 3;
  string subject = 4;
  string body = 5;
}

message Issuer15GFormRequest {
  string isin = 1;
  double annual_income = 2;
}