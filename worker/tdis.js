/**
 * Handle TDIS postback
 *
 * @param {Request} request The cloudflare HTTP request
 * @returns {Promise<Response>} A response indicating success
 */
export async function handleTDISPostback(request) {
  // Parse the form data and log
  const formData = new URLSearchParams(await request.text());
  console.log("Received TDIS Postback", Object.fromEntries(formData.entries()));
  return new Response("OK");
}
