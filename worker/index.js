import { handleSitemapUrl } from "./sitemap.js";
import { handleTDISPostback } from "./tdis.js";

const BOT_AGENTS = [
  "googlebot",
  "yahoo! slurp",
  "bingbot",
  "yandex",
  "baiduspider",
  "facebookexternalhit",
  "twitterbot",
  "rogerbot",
  "linkedinbot",
  "embedly",
  "quora link preview",
  "showyoubot",
  "outbrain",
  "pinterest/0.",
  "developers.google.com/+/web/snippet",
  "slackbot",
  "vkshare",
  "w3c_validator",
  "redditbot",
  "applebot",
  "whatsapp",
  "flipboard",
  "tumblr",
  "bitlybot",
  "skypeuripreview",
  "nuzzel",
  "discordbot",
  "google page speed",
  "qwantify",
  "pinterestbot",
  "bitrix link preview",
  "xing-contenttabreceiver",
  "chrome-lighthouse",
  "telegrambot",
  "OAI-SearchBot",
  "ChatGPT",
  "GPTBot",
  "ClaudeBot",
  "Amazonbot",
  "Perplexity",
  "google-inspectiontool",
  "integration-test", // Integration testing
];

export default {
  /**
   * @param {Request} request
   * @param {Cloudflare.Env} env
   */
  async fetch(request, env) {
    const url = new URL(request.url);
    if (
      url.pathname.startsWith("/sitemap/") ||
      url.pathname.startsWith("/sitemap.xml")
    ) {
      return handleSitemapUrl(url);
    }
    if (url.pathname.startsWith("/internal/tdis")) {
      return handleTDISPostback(request);
    }
    const userAgent = request.headers.get("User-Agent")?.toLowerCase() || "";
    const isPrerender = request.headers.get("X-Prerender");
    const isHotwireNative = userAgent.includes("hotwire native");
    const accessToken =
      parseCookies(request.headers.get("Cookie") || "")["sm_auth_token"] || "";
    const refreshToken =
      parseCookies(request.headers.get("Cookie") || "")[
        "sm_refresh_auth_token"
      ] || "";
    if (isPrerender || !BOT_AGENTS.some((bot) => userAgent.includes(bot))) {
      const indexRequest = new Request(new URL("/", request.url));
      if (!accessToken && !refreshToken) {
        return env.ASSETS.fetch(indexRequest);
      }
      const response = await env.ASSETS.fetch(indexRequest);
      const newHeaders = new Headers(response.headers);
      newHeaders.set("Document-Policy", "js-profiling");
      const maxAgeSeconds = 273 * 24 * 60 * 60;
      if (accessToken && isHotwireNative) {
        newHeaders.append(
          "Set-Cookie",
          `sm_access_token=${encodeURIComponent(accessToken)}; Path=/; Secure; SameSite=Lax; Max-Age=${maxAgeSeconds}; Expires=${getFutureDate(273)}`
        );
      }
      if (refreshToken && isHotwireNative) {
        newHeaders.append(
          "Set-Cookie",
          `sm_refresh_token=${encodeURIComponent(refreshToken)}; Path=/; Secure; SameSite=Lax; Max-Age=${maxAgeSeconds}; Expires=${getFutureDate(273)}`
        );
      }
      return new Response(response.body, {
        status: response.status,
        headers: newHeaders,
      });
    }
    const newURL = `https://prerender.stablemoney.in/render?url=${request.url}`;
    const newHeaders = new Headers(request.headers);
    newHeaders.set("X-Prerender-Int-Type", "CloudFlare");
    return fetch(
      new Request(newURL, {
        headers: newHeaders,
        redirect: "manual",
      })
    );
  },
};

// Helper: Cookie parsing
/**
 * @param {string} cookieHeader
 */
function parseCookies(cookieHeader) {
  const out = /** @type {Record<string, string>} */ ({});
  for (const part of cookieHeader.split(";")) {
    const [name, ...rest] = part.trim().split("=");
    if (name && rest.length > 0) {
      out[name] = decodeURIComponent(rest.join("="));
    }
  }
  return out;
}

/**
 * @param {number} daysAhead
 */
function getFutureDate(daysAhead) {
  const date = new Date();
  date.setUTCDate(date.getUTCDate() + daysAhead);
  return date.toUTCString(); // e.g. "Wed, 24 Mar 2026 10:00:00 GMT"
}
